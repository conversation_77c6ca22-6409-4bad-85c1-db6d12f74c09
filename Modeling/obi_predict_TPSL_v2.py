import pandas as pd
import numpy as np
import glob
import matplotlib.pyplot as plt
import multiprocessing
import os

import time
from sklearn.model_selection import RandomizedSearchCV, TimeSeriesSplit
from skopt import BayesSearchCV
from sklearn.metrics import accuracy_score, recall_score, f1_score, confusion_matrix
from sklearn.metrics import precision_score
from xgboost import XGBClassifier
from sklearn.linear_model import LogisticRegression
from xgboost.callback import EarlyStopping
from sklearn.preprocessing import StandardScaler
from collections import deque
from scipy.stats import uniform, randint
from sklearn.base import BaseEstimator, ClassifierMixin, clone
from sklearn.metrics import make_scorer, log_loss
from sklearn.utils import shuffle
from tqdm import tqdm
from skopt.space import Real, Integer, Categorical
from sklearn.decomposition import PCA
from skopt import gp_minimize
from sklearn.utils.class_weight import compute_sample_weight

class ShuffleWrapper(BaseEstimator, ClassifierMixin):
    def __init__(self, base_estimator=None, random_state=42):
        self.base_estimator = base_estimator
        self.random_state = random_state

    def fit(self, X, y, **fit_params):
        X_shuffled, y_shuffled = shuffle(X, y, random_state=self.random_state)
        self.base_estimator_ = clone(self.base_estimator)
        return self.base_estimator_.fit(X_shuffled, y_shuffled, **fit_params)

    def predict(self, X):
        return self.base_estimator_.predict(X)

    def predict_proba(self, X):
        return self.base_estimator_.predict_proba(X)

    def score(self, X, y):
        return self.base_estimator_.score(X, y)

    def get_params(self, deep=True):
        out = {"base_estimator": self.base_estimator, "random_state": self.random_state}
        if deep and hasattr(self.base_estimator, "get_params"):
            for key, value in self.base_estimator.get_params(deep=True).items():
                out[f"{key}"] = value  # 展開不加前綴
        return out

    def set_params(self, **params):
        base_params = {}
        for key, value in params.items():
            if key in ["base_estimator", "random_state"]:
                setattr(self, key, value)
            else:
                base_params[key] = value
        if self.base_estimator is not None:
            self.base_estimator.set_params(**base_params)
        return self

# --------------------------------------------------------------------------------
# 0. Parallel & Parameters
# --------------------------------------------------------------------------------
n_cpu        = multiprocessing.cpu_count()
n_jobs       = max(1, n_cpu - 3)
coin         = "BTC_USD"
selected_layers = list(range(1,21))
of_ma_windows   = [1,6,30,60,120,300,600,1200,2400,4800]
maker_fee = 0.0006
taker_fee = 0.0016
w_tp, w_none, w_sl = 5.0, 1.0, 1.0

# --------------------------------------------------------------------------------
# 1. Load Orderbook
# --------------------------------------------------------------------------------
print("=== 1. Loading orderbook files ===")
files = glob.glob(f"../kraken_data/{coin}_orderbook_*.feather")
dfs   = [pd.read_feather(f) for f in files]
df_ob = pd.concat(dfs, ignore_index=True)

# --------------------------------------------------------------------------------
# 2. Calculate OBI & Raw Volumes & Mid/Bid/Ask
# --------------------------------------------------------------------------------
print("=== 2. Computing OBI snapshots ===")

# Check if precomputed features exist
feature_file = f"../{coin}_computed_features.feather"
if os.path.exists(feature_file):
    print("Loading precomputed features...")
    df_features = pd.read_feather(feature_file)
    df_features.set_index('index', inplace=True)
    df_features.index = pd.to_datetime(df_features.index)
    df_obi = df_features.filter(regex='^OBI_')
    df_vol = df_features.filter(regex='^BidVol_|^AskVol_')
    mid_s = df_features['MidPrice']
    bb_s = df_features['BestBid']
    ba_s = df_features['BestAsk']
    times = pd.to_datetime(df_features.index)
else:
    cutoff, seg, snap_sz = 0.01, 40, 1000
    times, mids, bbs, bas = [], [], [], []
    obi_list, vol_list   = [], []

    for i in tqdm(range(len(df_ob)//snap_sz), desc="Snapshots"):
        snap = df_ob.iloc[i*snap_sz:(i+1)*snap_sz]
        srt  = snap.sort_values("Price").reset_index(drop=True)

        bids = srt.iloc[:500].nlargest(1,"Price")["Price"].iloc[0]
        asks = srt.iloc[500:].nsmallest(1,"Price")["Price"].iloc[0]
        mid  = 0.5*(bids+asks)

        lower, upper = mid*(1-cutoff), mid*(1+cutoff)
        rng = srt[srt["Price"].between(lower,upper)].copy()

        if rng.empty:
            obi_row = [np.nan]*len(selected_layers)
            vol_row = [np.nan]*2*len(selected_layers)
        else:
            bins = np.linspace(lower, upper, seg+1)
            rng["Bin"] = pd.cut(rng["Price"], bins=bins, labels=False, include_lowest=True)
            bybin = rng.groupby("Bin")["Size"].sum().reindex(range(seg), fill_value=0).values
            obi_row, vol_row = [], []
            half = seg//2
            for k in selected_layers:
                Vb = bybin[half-k:half].sum()
                Va = bybin[half:half+k].sum()
                obi_row.append((Vb-Va)/(Vb+Va) if (Vb+Va)!=0 else np.nan)
                vol_row += [Vb, Va]

        obi_list.append(obi_row)
        vol_list.append(vol_row)
        times.append(srt["Time"].iloc[0])
        mids.append(mid)
        bbs.append(bids)
        bas.append(asks)

    times = pd.to_datetime(times)
    col_obi = [f"OBI_{k}" for k in selected_layers]
    df_obi  = pd.DataFrame(obi_list, index=times, columns=col_obi)
    col_vol = sum([[f"BidVol_{k}",f"AskVol_{k}"] for k in selected_layers],[])
    df_vol  = pd.DataFrame(vol_list, index=times, columns=col_vol)
    mid_s   = pd.Series(mids, index=times, name="MidPrice")
    bb_s    = pd.Series(bbs, index=times, name="BestBid")
    ba_s    = pd.Series(bas, index=times, name="BestAsk")

    # Save computed features
    print("Saving computed features...")
    df_features = pd.concat([df_obi, df_vol, mid_s, bb_s, ba_s], axis=1)
    df_features.columns = df_features.columns.astype(str)  # Convert column names to strings
    df_features.reset_index().to_feather(feature_file)

# Round to 10s and dedupe
df_obi.index = df_obi.index.round("10S")
df_obi       = df_obi[~df_obi.index.duplicated(keep="last")]
df_vol.index = df_vol.index.round("10S")
df_vol       = df_vol[~df_vol.index.duplicated(keep="last")]
mid_s.index  = mid_s.index.round("10S"); mid_s  = mid_s[~mid_s.index.duplicated()]
bb_s.index   = bb_s.index.round("10S");  bb_s   = bb_s[~bb_s.index.duplicated()]
ba_s.index   = ba_s.index.round("10S");  ba_s   = ba_s[~ba_s.index.duplicated()]

# --------------------------------------------------------------------------------
# 3. Build uniform time grid
# --------------------------------------------------------------------------------
full_index = pd.date_range(start=df_obi.index.min(), end=df_obi.index.max(), freq="10S")

# --------------------------------------------------------------------------------
# 4. Aggregate trade ticks → order_flow & TradeCount
# --------------------------------------------------------------------------------
print("=== 4. Aggregating trades ===")

# Check if precomputed trade data exists
trade_file = f"../{coin}_computed_trades.feather"
if os.path.exists(trade_file):
    print("Loading precomputed trade data...")
    df_trades = pd.read_feather(trade_file)
    df_trades.set_index('index', inplace=True)
    df_trades.index = pd.to_datetime(df_trades.index)
    # Ensure column names match exactly
    df_trades.columns = ['order_flow', 'TradeCount_10s']
    order_flow_acc = df_trades['order_flow']
    trade_count_10s = df_trades['TradeCount_10s']
else:
    trade_files      = glob.glob(f"../kraken_data/{coin}_trades_*.feather")
    order_flow_acc   = pd.Series(0.0, index=full_index, name="order_flow")
    trade_count_10s  = pd.Series(0,   index=full_index, name="TradeCount_10s")

    for f in tqdm(trade_files, desc="Trades"):
        df_t = pd.read_feather(f).rename(columns={"Price":"price","Size":"amount","Time":"time","Side":"side"})
        df_t["time"] = pd.to_datetime(df_t["time"])
        df_t["signed"] = np.where(df_t["side"].str.lower()=="buy", df_t["amount"], -df_t["amount"])
        df_t = df_t.set_index("time")
        flow10 = df_t["signed"].groupby(pd.Grouper(freq="10S")).sum().reindex(full_index, fill_value=0)
        cnt10  = df_t["signed"].groupby(pd.Grouper(freq="10S")).count().reindex(full_index, fill_value=0)
        order_flow_acc  = order_flow_acc.add(flow10, fill_value=0)
        trade_count_10s = trade_count_10s.add(cnt10,  fill_value=0)

    order_flow_acc.fillna(0, inplace=True)

    # Save computed trade data
    print("Saving computed trade data...")
    df_trades = pd.DataFrame({
        'order_flow': order_flow_acc,
        'TradeCount_10s': trade_count_10s
    })
    df_trades.reset_index().to_feather(trade_file)

# --------------------------------------------------------------------------------
# 5. Build OF features
# --------------------------------------------------------------------------------
print("=== 5. Building OF features ===")
# 6/19/2025
# Step 1: fill missing timestamps on order flow and trade count
order_flow_acc = order_flow_acc.reindex(full_index).ffill(limit=2)
trade_count_10s = trade_count_10s.reindex(full_index).ffill(limit=2)

# Step 2: compute base signals
of_z     = (order_flow_acc - order_flow_acc.mean()) / order_flow_acc.std()
of_diff  = of_z.diff().fillna(0)
of_cum1m = order_flow_acc.rolling(6, min_periods=1).sum()

# Step 3: compute new features
of_vol = of_z.rolling(30, min_periods=1).std().rename("OF_vol")
of_mom = of_z.rolling(60, min_periods=1).mean().rename("OF_mom")
of_acc = of_diff.rolling(30, min_periods=1).mean().rename("OF_acc")

# Step 4: construct base df_of
df_of = pd.concat([
    of_z.rename("OF_z"),
    of_diff.rename("OF_diff"),
    of_cum1m.rename("OF_cum1m"),
    of_vol,
    of_mom,
    of_acc
], axis=1)

# Step 5: add rolling features with different windows
for w in of_ma_windows:
    df_of[f"OF_z_ma_{w}"]    = of_z.rolling(w, min_periods=1).mean()
    df_of[f"OF_diff_ma_{w}"] = of_diff.rolling(w, min_periods=1).mean()
    df_of[f"OF_vol_ma_{w}"]  = of_vol.rolling(w, min_periods=1).mean()
    df_of[f"OF_mom_ma_{w}"]  = of_mom.rolling(w, min_periods=1).mean()
    df_of[f"OF_acc_ma_{w}"]  = of_acc.rolling(w, min_periods=1).mean()

# --------------------------------------------------------------------------------
# 6. Forward-fill OBI, VOL, MID, BID, ASK
# --------------------------------------------------------------------------------
def ffill(s): return s.reindex(full_index).ffill(limit=2)
df_obi_f = ffill(df_obi)
df_vol_f = ffill(df_vol)
mid_f    = ffill(mid_s)
bb_f     = ffill(bb_s)
ba_f     = ffill(ba_s)

# Derived features
spread  = (ba_f - bb_f).rename("Spread")
ma_s    = mid_f.rolling(6,min_periods=1).mean()
ma_l    = mid_f.rolling(30,min_periods=1).mean()
ma_diff = (ma_s - ma_l).rename("MA_diff")
vol1m   = mid_f.pct_change().rolling(6,min_periods=1).std().rename("Vol1m")

# 新增：价格波动率特征
vol_short = mid_f.pct_change().rolling(30, min_periods=1).std().rename("Vol_short")
vol_medium = mid_f.pct_change().rolling(60, min_periods=1).std().rename("Vol_medium")
vol_long = mid_f.pct_change().rolling(120, min_periods=1).std().rename("Vol_long")

# 新增：价格动量特征
mom_short = mid_f.pct_change(30).rolling(30, min_periods=1).mean().rename("Mom_short")
mom_medium = mid_f.pct_change(60).rolling(60, min_periods=1).mean().rename("Mom_medium")
mom_long = mid_f.pct_change(120).rolling(120, min_periods=1).mean().rename("Mom_long")

# 新增：价格加速度特征
acc_short = mid_f.pct_change().rolling(30, min_periods=1).mean().rename("Acc_short")
acc_medium = mid_f.pct_change().rolling(60, min_periods=1).mean().rename("Acc_medium")
acc_long = mid_f.pct_change().rolling(120, min_periods=1).mean().rename("Acc_long")

# 新增：买卖压力特征
buy_pressure = (df_vol_f.filter(regex='^BidVol_').sum(axis=1) / 
               (df_vol_f.filter(regex='^BidVol_').sum(axis=1) + 
                df_vol_f.filter(regex='^AskVol_').sum(axis=1))).rename("Buy_pressure")

# 新增：深度特征
depth = (df_vol_f.filter(regex='^BidVol_').sum(axis=1) + 
         df_vol_f.filter(regex='^AskVol_').sum(axis=1)).rename("Depth")

# 新增：OBI变化率特征
obi_change = df_obi_f.pct_change().fillna(0)
obi_vol = obi_change.mean(axis=1).rolling(30, min_periods=1).std().rename("OBI_vol")
obi_mom = obi_change.mean(axis=1).rolling(60, min_periods=1).mean().rename("OBI_mom")
obi_acc = obi_change.mean(axis=1).diff().rolling(30, min_periods=1).mean().rename("OBI_acc")

# Trade feature
trade1m = trade_count_10s.rolling(6,min_periods=1).sum().rename("TradeCnt1m")
df_trade= trade1m.to_frame()

# 新增：交易量特征
trade_vol = trade_count_10s.rolling(30, min_periods=1).std().rename("Trade_vol")
trade_mom = trade_count_10s.rolling(60, min_periods=1).mean().rename("Trade_mom")
trade_acc = trade_count_10s.diff().rolling(30, min_periods=1).mean().rename("Trade_acc")

# --------------------------------------------------------------------------------
# 7. Pre-compute multi-scale features
# --------------------------------------------------------------------------------
feats_list = []

# 1) OBI and raw vol in different windows rolling average
for w in of_ma_windows:
    obi_w = df_obi_f.rolling(w, min_periods=1).mean().add_prefix(f"OBI_ma{w}_")
    vol_w = df_vol_f.rolling(w, min_periods=1).mean().add_prefix(f"VOL_ma{w}_")
    feats_list.append(obi_w)
    feats_list.append(vol_w)

# 2) Derived features spread, ma_diff, vol1m
deriv = pd.concat([spread, ma_diff, vol1m, 
                  vol_short, vol_medium, vol_long,
                  mom_short, mom_medium, mom_long,
                  acc_short, acc_medium, acc_long,
                  buy_pressure, depth,
                  obi_vol, obi_mom, obi_acc,
                  trade_vol, trade_mom, trade_acc
                  ], axis=1)
deriv = deriv.reindex(full_index).ffill(limit=2)
for w in of_ma_windows:
    der_w = deriv.rolling(w, min_periods=1).mean().add_prefix(f"DER_ma{w}_")
    feats_list.append(der_w)

# 3) order_flow_acc cumulative and df_of already existing of_z_ma_* / of_diff_ma_*
for w in of_ma_windows:
    ofcum = order_flow_acc.rolling(w, min_periods=1).sum().rename(f"OF_cum{w}")
    feats_list.append(ofcum)

# 4) df_of itself already contains OF_z, OF_diff, OF_cum1m, OF_z_ma_*, OF_diff_ma_*:
feats_list.append(df_of)

# 5) Trade count rolling mean
for w in of_ma_windows:
    trd_w = df_trade.rolling(w, min_periods=1).mean().add_prefix(f"TRD_ma{w}_")
    feats_list.append(trd_w)

# Concat all features
feats_base = pd.concat(feats_list, axis=1).ffill(limit=2).dropna(how="any")

# --------------------------------------------------------------------------------
# 7. PCA
# --------------------------------------------------------------------------------
# print("=== 7. PCA ===")
# print("Dimension before PCA:", feats_base.shape[1])
# scaler = StandardScaler()
# X_scaled = scaler.fit_transform(feats_base)
# pca = PCA(n_components=0.99)
# X_pca = pca.fit_transform(X_scaled)
# print("Dimension after PCA:", X_pca.shape[1])
# feats_base = pd.DataFrame(X_pca, index=feats_base.index, columns=["PCA_" + str(i) for i in range(X_pca.shape[1])])
# --------------------------------------------------------------------------------
# 8. Train classifiers
# --------------------------------------------------------------------------------
print("=== 8. Training classifiers ===")
# lags_dict = {"30min":180, "1hr":360, "2hr":720, "4hr":1440, "8hr":2880}
lags_dict = {"4hr":1440, "8hr":2880, "12hr":4320, "1day":8640}
# lags_dict = {"4hr":1440}
# lags_dict = {"1min":6, "5min":30, "10min":60, "15min":90, "30min":180}
model_dict = {
    # "RandomForest": RandomForestClassifier(n_jobs=n_jobs, random_state=42),
    # "Logistic":    LogisticRegression(max_iter=1000,n_jobs=n_jobs,random_state=42),
    "XGBoost":     XGBClassifier(objective="multi:softprob", num_class=3,
                                 eval_metric="mlogloss", 
                                 n_jobs=n_jobs, random_state=42)
}

tscv            = TimeSeriesSplit(n_splits=3)
tp_mult, sl_mult= 1.5, 1.0

accuracy_matrix = pd.Series(index=lags_dict.keys(), dtype=float)
recall_matrix   = pd.Series(index=lags_dict.keys(), dtype=float)
f1_matrix       = pd.Series(index=lags_dict.keys(), dtype=float)
confusion_mats  = {}
preds           = {}

avg_vol_tp_tp_df = pd.Series(index=lags_dict.keys(), dtype=float)
avg_vol_tp_sl_df = pd.Series(index=lags_dict.keys(), dtype=float)

best_models = {} 

for label,lag in tqdm(lags_dict.items(), desc=f"Horizons", leave=False):
    # Generate three-class label
    vol_h = mid_f.pct_change().rolling(window=lag, min_periods=1).std().fillna(0) * np.sqrt(lag)
    # vol_h = mid_f.pct_change().rolling(window=lag, min_periods=1).std().fillna(0)
    
    tp_price = mid_f*(1+tp_mult*vol_h)
    sl_price = mid_f*(1-sl_mult*vol_h)
    labs, times_l = [], []
    prs, tpv, slv = mid_f.values, tp_price.values, sl_price.values
    for i in range(len(prs)-lag):
        fut = prs[i+1:i+1+lag]
        t_hit = np.where(fut>=tpv[i])[0]
        s_hit = np.where(fut<=slv[i])[0]
        if  t_hit.size and (not s_hit.size or t_hit[0]<s_hit[0]):
            labs.append(1)
        elif s_hit.size and (not t_hit.size or s_hit[0]<t_hit[0]):
            labs.append(-1)
        else:
            labs.append(0)
        times_l.append(full_index[i])
    label_series = pd.Series(labs, index=times_l, name="label")
    # Map to 0,1,2
    label_series = label_series.map({-1:0, 0:1, 1:2})

    # Feature alignment
    common_index = feats_base.index.intersection(label_series.index)
    feats = feats_base.loc[common_index].dropna(how="any")
    y_lab = label_series.loc[common_index]

    if y_lab.nunique()<2:
        continue

    # train/test split
    days      = feats.index.normalize().unique()
    train_days = np.sort(days)[:-7]
    test_days = np.sort(days)[-7:]
    test_start = test_days.min()
    test_end   = test_days.max()
    trn_idx   = feats.index.normalize().isin(train_days)
    tst_idx   =  feats.index.normalize().isin(test_days)
    X_tr, X_te = feats.loc[trn_idx], feats.loc[tst_idx]
    y_tr, y_te = y_lab.loc[trn_idx], y_lab.loc[tst_idx]

    # Plot price time series with labels colored by TP/None/SL
    print(f"=== Plotting price time series for {label} ===")
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), sharex=True)
    
    # Get price data for the common index
    price_data = mid_f.reindex(common_index)
    
    # Create color mapping for labels
    colors = {2: 'green', 1: 'gray', 0: 'red'}  # TP=green, None=gray, SL=red
    labels_map = {2: 'TP', 1: 'None', 0: 'SL'}
    
    # Plot all data points
    for label_val in [2, 1, 0]:
        mask = y_lab == label_val
        if mask.any():
            ax1.scatter(price_data.index[mask], price_data[mask], 
                       c=colors[label_val], s=10, alpha=0.6, 
                       label=f'{labels_map[label_val]} ({mask.sum()})')
    
    # Plot train/test split line
    train_end_time = train_days[-1] + pd.Timedelta(days=1)
    ax1.axvline(x=train_end_time, color='blue', linestyle='--', alpha=0.7, 
                label=f'Train/Test Split ({pd.Timestamp(train_end_time).strftime("%Y-%m-%d")})')
    
    ax1.plot(price_data.index, price_data, 'k-', alpha=0.3, linewidth=0.5)
    ax1.set_ylabel('Price')
    ax1.set_title(f'Price Time Series with Labels - {label} (TP/None/SL)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot label distribution over time
    for label_val in [2, 1, 0]:
        mask = y_lab == label_val
        if mask.any():
            ax2.scatter(price_data.index[mask], [label_val] * mask.sum(), 
                       c=colors[label_val], s=10, alpha=0.6)
    
    ax2.axvline(x=train_end_time, color='blue', linestyle='--', alpha=0.7)
    ax2.set_ylabel('Label')
    ax2.set_xlabel('Time')
    ax2.set_title('Label Distribution Over Time')
    ax2.set_yticks([0, 1, 2])
    ax2.set_yticklabels(['SL', 'None', 'TP'])
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Print label distribution statistics
    print(f"Label distribution:")
    for label_val in [2, 1, 0]:
        count = (y_lab == label_val).sum()
        percentage = count / len(y_lab) * 100
        print(f"  {labels_map[label_val]}: {count} ({percentage:.1f}%)")
    
    print(f"Train set size: {len(X_tr)}, Test set size: {len(X_te)}")
    print(f"Train period: {pd.Timestamp(train_days[0]).strftime('%Y-%m-%d')} to {pd.Timestamp(train_days[-1]).strftime('%Y-%m-%d')}")
    print(f"Test period: {pd.Timestamp(test_days[0]).strftime('%Y-%m-%d')} to {pd.Timestamp(test_days[-1]).strftime('%Y-%m-%d')}")
    print()

    if len(X_tr)<20 or len(X_te)<20: 
        continue

    # hyperparam search & train
    key, est = next(iter(model_dict.items()))
    # param_dist, n_iter = {
    #     "XGBoost": ({"n_estimators":[100,200,500],"max_depth":[3,6,10],
    #                 "learning_rate":[0.01,0.05,0.1],"subsample":[0.5,1.0],
    #                 "colsample_bytree":[0.5,1.0], "reg_lambda":[1, 5, 10]}, 5)
    # }[key]
    param_dist, n_iter = {
        "XGBoost": 
                    # ({"n_estimators":[100,200,500,1000],"max_depth":[1,2,3],"min_child_weight":[3, 5, 7],
                    # "learning_rate":[0.001, 0.005, 0.01],"subsample":[0.3,0.5],
                    # "colsample_bytree":[0.3,0.5], "reg_lambda":[50, 100, 200], "reg_alpha":[10, 50, 100]
                    # }, 5),
                    ({"n_estimators":[806],"max_depth":[9],"min_child_weight":[6],
                    "learning_rate":[0.0024024437596448125],"subsample":[0.3371768682733291],
                    "colsample_bytree":[1.0], "reg_lambda":[125], "reg_alpha":[32]
                    }, 1),
        "Logistic": ({"C": [0.001, 0.01, 0.1, 1, 10, 100],   # 正则强度的倒数
            "penalty": ["l1", "l2"],               # L1 / L2 正则
            "solver": ["liblinear", "saga"]        # 支持 l1 的 solver
        },
        10                                       # 尝试 10 组配置
    )
    }[key]
    param_space = {
        "XGBoost": {
            "n_estimators": Integer(100, 1000, name="n_estimators"),
            "max_depth": Integer(1, 10, name="max_depth"),
            # "min_child_weight": Integer(1, 10, name="min_child_weight"),
            "learning_rate": Real(0.001, 0.01, prior='log-uniform', name="learning_rate"),
            "subsample": Real(0.0, 1.0, name="subsample"),
            "colsample_bytree": Real(0.0, 1.0, name="colsample_bytree"),
            # "reg_lambda": Integer(0, 200, name="reg_lambda"),
            # "reg_alpha": Integer(0, 100, name="reg_alpha"),
            "gamma": Real(0, 0.1, name="gamma"),
            "reg_lambda": Real(0.1, 500, prior='log-uniform', name="reg_lambda"),
            "reg_alpha": Real(0.1, 200, prior='log-uniform', name="reg_alpha"),
            "min_child_weight": Integer(5, 50, name="min_child_weight"),
        },
        "Logistic": {
            "C": Real(0.001, 100, prior='log-uniform'),
            "penalty": Categorical(["l1", "l2"]),
            "solver": Categorical(["liblinear", "saga"])
        }
    }[key]

    def ev_from_proba(y_true, y_proba, vol_h_series):
        preds      = np.argmax(y_proba, axis=1)
        tp_mask    = preds == 2
        if not tp_mask.any(): return -0.1

        long_prec   = np.mean(y_true[tp_mask] == 2)
        tp_sl_ratio = np.mean(y_true[tp_mask] == 0)
        p_none =      np.mean(y_true[tp_mask] == 1)

        mask_tp_tp = tp_mask & (y_true == 2)
        mask_tp_sl = tp_mask & (y_true == 0)

        v_tp = np.mean(vol_h_series[mask_tp_tp]) if mask_tp_tp.any() else 0.0
        v_sl = np.mean(vol_h_series[mask_tp_sl]) if mask_tp_sl.any() else 0.0

        g  = tp_mult * v_tp
        l  = sl_mult * v_sl
        ev = (long_prec * ( (1 + g) * (1 - maker_fee) / (1 + maker_fee) - 1 )
                # + p_none * ( (2+g-l)/2 * (1 - taker_fee) / (1 + maker_fee) - 1 )
                + p_none * ( 1 * (1 - taker_fee) / (1 + maker_fee) - 1 )
                + tp_sl_ratio  * ( (1 - l) * (1 - taker_fee) / (1 + maker_fee) - 1 ))

        return ev

    def ev_score(estimator, X_val, y_val):
        # # Create a copy of the estimator with early stopping
        # if isinstance(estimator, XGBClassifier):
        #     est = XGBClassifier(
        #         **{k: v for k, v in estimator.get_params().items() 
        #            if k not in ['early_stopping_rounds', 'eval_set']},
        #         early_stopping_rounds=10
        #     )
        #     # Split validation set into train and validation for early stopping
        #     tscv_inner = TimeSeriesSplit(n_splits=2)
        #     for train_idx, val_idx in tscv_inner.split(X_val):
        #         X_train, X_val_inner = X_val.iloc[train_idx], X_val.iloc[val_idx]
        #         y_train, y_val_inner = y_val.iloc[train_idx], y_val.iloc[val_idx]
        #         est.fit(
        #             X_train, y_train,
        #             eval_set=[(X_val_inner, y_val_inner)],
        #             verbose=False
        #         )
        #         break  # We only need the first split
        #     proba = est.predict_proba(X_val)
        # else:
        #     proba = estimator.predict_proba(X_val)
        
        proba = estimator.predict_proba(X_val)
        vol_h_sub = vol_h.reindex(X_val.index).to_numpy()
        return ev_from_proba(y_val.to_numpy(), proba, vol_h_sub)

    wrapped_est = ShuffleWrapper(est)
    # search = RandomizedSearchCV(estimator=est,
    #                             param_distributions=param_dist,
    #                             n_iter=n_iter,
    #                             cv=tscv,
    #                             scoring=ev_score,
    #                             # scoring="accuracy",
    #                             return_train_score=True,
    #                             n_jobs=n_jobs,
    #                             random_state=42,
    #                             verbose=1,
    #                             refit=False)
    # search = BayesSearchCV(estimator=wrapped_est,
    #                       search_spaces=param_space,
    #                       n_iter=15,
    #                       cv=tscv,
    #                     #   scoring="accuracy",
    #                       scoring=ev_score,
    #                       return_train_score=True,
    #                       n_jobs=n_jobs,
    #                       random_state=42,
    #                       verbose=3,
    #                       refit=False)

    # search.fit(X_tr, y_tr)
    # # #取出最优超参
    # best_params = search.best_params_
    X = X_tr
    y = y_tr
    search_space = list(param_space.values())
    param_names = [dim.name for dim in search_space]
    def objective(params):
        cfg = dict(zip(param_names, params),
                objective='multi:softprob',
                num_class=3,
                eval_metric='mlogloss',
                early_stopping_rounds=30,
                random_state=42,
                n_jobs=n_jobs)

        start_candidate = time.time()
        print(f"Fitting {tscv.get_n_splits()} folds for candidate: {cfg}")

        fold_train_losses = []
        fold_test_losses  = []
        fold_train_evs    = []
        fold_test_evs     = []

        for i, (train_idx, test_idx) in enumerate(tscv.split(X), start=1):
            # 切 train/val
            X_train, y_train = X.iloc[train_idx], y.iloc[train_idx]
            X_val,   y_val   = X.iloc[test_idx],  y.iloc[test_idx]
            X_train, y_train = shuffle(X_train, y_train, random_state=42)

            cw = {2: w_tp, 1: w_none, 0: w_sl}
            sw = compute_sample_weight(class_weight=cw, y=y_train)
            # fit + early stop
            start_fold = time.time()
            model = XGBClassifier(**cfg)
            model.fit(
                X_train, y_train,
                sample_weight=sw,
                eval_set=[(X_val, y_val)],
                verbose=False
            )
            elapsed = time.time() - start_fold

            # train 預測
            proba_tr = model.predict_proba(X_train)
            pred_tr = model.predict(X_train)
            loss_tr  = log_loss(y_train, proba_tr)
            ev_tr    = ev_from_proba(y_train.to_numpy(),
                                    proba_tr,
                                    vol_h.reindex(X_train.index).to_numpy())
            fold_train_losses.append(loss_tr)
            fold_train_evs.append(ev_tr)

            # test 預測
            proba_te = model.predict_proba(X_val)
            pred_te = model.predict(X_val)
            loss_te  = log_loss(y_val, proba_te)
            ev_te    = ev_from_proba(y_val.to_numpy(),
                                    proba_te,
                                    vol_h.reindex(X_val.index).to_numpy())
            fold_test_losses.append(loss_te)
            fold_test_evs.append(ev_te)

            # Print confusion matrices
            cm_train = confusion_matrix(y_train, pred_tr, labels=[2,1,0])
            cm_test = confusion_matrix(y_val, pred_te, labels=[2,1,0])
            
            # Calculate metrics for train
            tp_pred_train = (pred_tr == 2).sum()
            tp_true_train = ((pred_tr == 2) & (y_train == 2)).sum()
            long_prec_train = tp_true_train / tp_pred_train if tp_pred_train > 0 else np.nan
            tp_sl_ratio_train = ((pred_tr == 2) & (y_train == 0)).sum() / tp_pred_train if tp_pred_train > 0 else np.nan
            
            # Calculate metrics for test
            tp_pred_test = (pred_te == 2).sum()
            tp_true_test = ((pred_te == 2) & (y_val == 2)).sum()
            long_prec_test = tp_true_test / tp_pred_test if tp_pred_test > 0 else np.nan
            tp_sl_ratio_test = ((pred_te == 2) & (y_val == 0)).sum() / tp_pred_test if tp_pred_test > 0 else np.nan

            m, s = divmod(elapsed, 60)
            print(f"[CV {i}/{tscv.n_splits}] "
                f"loss=(train={loss_tr:.4f}, test={loss_te:.4f})  "
                f"ev=(train={ev_tr:.4f}, test={ev_te:.4f})  "
                f"time={int(m)}m{s:.1f}s")
            
            print(f"  Train CM (TP/None/SL):\n{cm_train}")
            print(f"  Train - Long Prec: {long_prec_train:.3f}, TP→SL ratio: {tp_sl_ratio_train:.3f}")
            print(f"  Test CM (TP/None/SL):\n{cm_test}")
            print(f"  Test - Long Prec: {long_prec_test:.3f}, TP→SL ratio: {tp_sl_ratio_test:.3f}")

        # candidate 總結
        mean_tr_loss = np.mean(fold_train_losses)
        mean_te_loss = np.mean(fold_test_losses)
        mean_tr_ev   = np.mean(fold_train_evs)
        mean_te_ev   = np.mean(fold_test_evs)
        tot = time.time() - start_candidate
        m, s = divmod(tot, 60)
        print(f"Candidate done. "
            f"mean_loss=(train={mean_tr_loss:.4f}, test={mean_te_loss:.4f}); "
            f"mean_ev=(train={mean_tr_ev:.4f}, test={mean_te_ev:.4f}); "
            f"total time={int(m)}m{s:.1f}s\n")

        # 我們要最大化 ev，所以回傳負的 test ev 平均
        return -mean_te_ev

    # 執行 Bayesian 優化
    res = gp_minimize(
        objective,
        search_space,
        n_calls=20,
        random_state=42,
        verbose=True       # 這裡會印出每次「呼叫第幾次 objective」的 summary
    )
    best_params = dict(zip(param_names, res.x))
    # 用最优超参重新构造一个「骨架」XGB
    print(best_params)
    val_frac = 0.3
    val_size = int(len(X_tr) * val_frac)
    X_tr_inner = X_tr.iloc[:-val_size]
    y_tr_inner = y_tr.iloc[:-val_size]
    X_val      = X_tr.iloc[-val_size:]
    y_val      = y_tr.iloc[-val_size:]

    cw = {2: w_tp, 1: w_none, 0: w_sl}
    sw = compute_sample_weight(class_weight=cw, y=y_tr_inner)
    if key == "XGBoost":
        best_base = XGBClassifier(
            objective="multi:softprob",
            num_class=3,
            eval_metric="mlogloss",
            n_jobs=n_jobs,
            early_stopping_rounds=30,
            random_state=42,
            **best_params
        )
        best = ShuffleWrapper(best_base)
        # 把早停放到 fit 里
        best.fit(
            # X_tr, y_tr,
            # eval_set=[(X_tr, y_tr), (X_te, y_te)],
            X_tr_inner, y_tr_inner,
            eval_set=[(X_val, y_val)],
            sample_weight=sw,
            # early_stopping_rounds=10,
            # eval_metric="mlogloss",
            verbose=True       # 打印每一轮的指标
        )
    else:  # Logistic
        best = LogisticRegression(
            max_iter=1000,
            n_jobs=n_jobs,
            random_state=42,
            **best_params
        )
    # best.fit(X_tr, y_tr)
    best_mod = best
    best_models[label] = best_mod
    # best_mod = search.best_estimator_
    # best_models[label] = search.best_estimator_
    # Predict & Evaluate
    y_pred = best_mod.predict(X_te)
    acc    = accuracy_score(y_te, y_pred)
    rec    = recall_score(y_te, y_pred, average="macro")
    f1s    = f1_score(y_te, y_pred, average="macro")
    cm     = confusion_matrix(y_te, y_pred, labels=[2,1,0])  # 2=TP,1=None,0=SL

    # New metrics:
    tp_pred_count  = np.sum(y_pred == 2)
    tp_true_count  = np.sum((y_pred == 2) & (y_te == 2))
    long_prec      = tp_true_count / tp_pred_count if tp_pred_count > 0 else np.nan
    tp_to_sl       = np.sum((y_pred == 2) & (y_te == 0))
    tp_sl_ratio    = tp_to_sl / tp_pred_count if tp_pred_count > 0 else np.nan

    vol_h_te = vol_h.reindex(X_te.index)
    mask_tp_tp     = (y_pred == 2) & (y_te == 2)
    avg_vol_tp_tp  = vol_h_te[mask_tp_tp].mean()
    mask_tp_sl     = (y_pred == 2) & (y_te == 0)
    avg_vol_tp_sl  = vol_h_te[mask_tp_sl].mean()
    
    # Store results
    avg_vol_tp_tp_df.loc[label] = avg_vol_tp_tp
    avg_vol_tp_sl_df.loc[label] = avg_vol_tp_sl
    accuracy_matrix.loc[label] = acc
    recall_matrix.loc[label]   = rec
    f1_matrix.loc[label]       = f1s
    confusion_mats[(label)]    = cm
    preds[(label)]             = (y_te, y_pred)

    # Console output
    print(f"[lag={label}] Acc={acc:.3f}, Rec={rec:.3f}, F1={f1s:.3f}")
    print(f"  Long Precision (TP class): {long_prec:.3f}")
    print(f"  TP→SL ratio: {tp_sl_ratio:.3f}")
    print("Confusion matrix:\n", cm)

    # best_idx = search.best_index_
    # n_folds = tscv.get_n_splits()
    # train_evs = [
    #     search.cv_results_[f"split{i}_train_score"][best_idx]
    #     for i in range(n_folds)
    # ]
    # val_evs = [
    #     search.cv_results_[f"split{i}_test_score"][best_idx]
    #     for i in range(n_folds)
    # ]
    # print("▶ Each fold training set EV:", train_evs)
    # print("▶ Each fold validation set EV:", val_evs)
    # test_proba = best_mod.predict_proba(X_te)
    # test_ev    = ev_from_proba(y_te.values, test_proba, vol_h_te)
    # print(f"  ➤ Test set EV score: {test_ev:.6f}")

    # # Visualize TP/SL levels and volatility
    # fig,(ax1,ax2)=plt.subplots(2,1,figsize=(12,6),sharex=True)
    # ax1.plot(full_index, mid_f,      label="Mid Price")
    # ax1.plot(full_index, tp_price,   "--", label="TP")
    # ax1.plot(full_index, sl_price,   "--", label="SL")
    # ax1.set_ylabel("Price"); ax1.legend(); ax1.grid(True)
    # ax2.plot(full_index, vol_h,      label="Volatility"); 
    # ax2.set_ylabel("Vol"); ax2.set_xlabel("Time"); ax2.legend(); ax2.grid(True)
    # plt.tight_layout(); plt.show()

# Store feature names for backtest
    if label == list(lags_dict.keys())[-1]:  # Last iteration
        if hasattr(best_mod, "base_estimator_"):
            feat_names = best_mod.base_estimator_.feature_names_in_
        else:
            feat_names = best_mod.feature_names_in_
# --------------------------------------------------------------------------------
# 10. Output results and visualize confusion matrix + long precision + TP→SL ratio
# --------------------------------------------------------------------------------
# 10.1 Print matrices
print("Accuracy:\n", accuracy_matrix)
print("Recall:\n", recall_matrix)
print("F1 Score:\n", f1_matrix)
print("=== Avg vol_h when TP→TP ===")
print(avg_vol_tp_tp_df)
print("=== Avg vol_h when TP→SL ===")
print(avg_vol_tp_sl_df)

# 10.2 Calculate Long Precision, TP→SL ratio, EV
long_prec, tp_sl_ratio, ev = {}, {}, {}
for label in preds:
    y_te, y_pred = preds[label]
    # Long Precision
    tp_pred = (y_pred == 2)
    tp_count = tp_pred.sum()
    long_prec[label] = ((y_te[tp_pred] == 2).sum() / tp_count) if tp_count>0 else np.nan
    # TP→SL ratio
    tp_sl_ratio[label] = ((y_pred==2)&(y_te==0)).sum() / tp_count if tp_count>0 else np.nan
    # EV (Reuse the formula from ev_from_proba)
    v_tp = avg_vol_tp_tp_df.loc[label]
    v_sl = avg_vol_tp_sl_df.loc[label]
    p_none = (y_pred==1).sum() / len(y_pred)
    ev[label] = (
        long_prec[label]*(v_tp*tp_mult - 2*maker_fee)
        - p_none*(taker_fee + maker_fee)
        - tp_sl_ratio[label]*(v_sl + taker_fee + maker_fee)
    )

lp_df    = pd.Series(long_prec,    name="LongPrec")
ratio_df = pd.Series(tp_sl_ratio, name="TP→SL Ratio")
ev_df    = pd.Series(ev,           name="EV")

print("\n=== Long Precision ===")
print(lp_df)
print("\n=== TP→SL Ratio ===")
print(ratio_df)
print("\n=== EV ===")
print(ev_df)

# 10.3 Plot multi-subplot Confusion Matrix with metrics below each subplot
n_plots   = len(lags_dict)
fig, axes = plt.subplots(1, n_plots,
                         figsize=(n_plots*3.5, 3.5),
                         constrained_layout=True)

if n_plots==1:
    axes = [axes]

for ax, label in zip(axes, lags_dict.keys()):
    cm = confusion_mats.get(label)
    if cm is None:
        ax.text(0.5, 0.5, "No Data", ha="center", va="center")
        continue

    im = ax.imshow(cm, cmap="Blues", vmin=0)
    for i in range(3):
        for j in range(3):
            ax.text(j, i, int(cm[i,j]), ha="center", va="center",
                    color="white" if cm[i,j]>cm.max()/2 else "black", fontsize=8)
            
    labels_cm = ["TP","None","SL"]
    ax.set_xticks([0,1,2]); ax.set_xticklabels(labels_cm, fontsize=8)
    ax.set_yticks([0,1,2]); ax.set_yticklabels(labels_cm, fontsize=8)
    ax.set_title(label, fontsize=9)

    # Add a line of metrics below the title
    txt = (
        f"LP={long_prec[label]:.2f}  "
        f"R={tp_sl_ratio[label]:.2f}\n"
        f"EV={ev[label]*100:.2f}%"
    )
    ax.text(0.5, -0.3, txt, transform=ax.transAxes,
            ha="center", va="top", fontsize=8,
            bbox=dict(boxstyle="round,pad=0.2", fc="white", ec="gray"))

fig.suptitle("Confusion Matrix & Metrics for Each Horizon", y=1.02)
plt.show()

# try:
#     feat_names = best_mod.feature_names_in_.tolist()
# except AttributeError:
    # feat_names = best_mod.get_booster().feature_names
# 11. Backtest (with correct fee scaling, PnL logging, and mismatch reporting)
if hasattr(best_mod, "base_estimator_"):
    feat_names = best_mod.base_estimator_.feature_names_in_
else:
    feat_names = best_mod.feature_names_in_
threshold     = 0.0
initial_cash  = 1.0
equity_results = {}


for label, lag in lags_dict.items():
    model = best_models[label]
    entry_cd    = pd.Timedelta(seconds=lag * 10 / 16)
    trade_log = []

    # rebuild features exactly as in training
    # X_full = feats_base.reindex(columns=model.feature_names_in_).dropna()
    X_full = feats_base.reindex(columns=feat_names).dropna()
    idx_full = X_full.index

    # restrict to test days
    mask_test = idx_full.normalize().isin(test_days)
    idx_bt    = idx_full[mask_test]

    # prepare probability series
    proba_df = pd.DataFrame(
        model.predict_proba(X_full),
        index=idx_full,
        columns=[0, 1, 2]  # Fixed classes for our 3-class problem
    )
    p_sl   = proba_df.get(0, pd.Series(0.0, idx_full))
    p_none = proba_df.get(1, pd.Series(0.0, idx_full))
    p_tp   = proba_df.get(2, pd.Series(0.0, idx_full))

    # compute forward vol and TP/SL levels
    vol_h    = mid_f.pct_change().rolling(lag, min_periods=1).std().fillna(0) * np.sqrt(lag)
    tp_price = mid_f * (1 + tp_mult * vol_h)
    sl_price = mid_f * (1 - sl_mult * vol_h)

    cash            = initial_cash
    positions       = deque()
    last_entry_time = idx_bt[0]
    ev_list, pos_flag, equity_list = [], [], []

    for t in idx_bt:
        # 1) compute EV
        g  = tp_mult * vol_h.loc[t]
        l  = sl_mult * vol_h.loc[t]
        # ev = (p_tp.loc[t] * (g - 2*maker_fee)
        #         - p_none.loc[t] * (taker_fee + maker_fee)
        #         - p_sl.loc[t]  * (l + taker_fee + maker_fee))
        ev = (p_tp.loc[t] * ( (1 + g) * (1 - maker_fee) / (1 + maker_fee) - 1 )
                # + p_none.loc[t] * ( (2+g-l)/2 * (1 - taker_fee) / (1 + maker_fee) - 1 )
                + p_none.loc[t] * ( 1 * (1 - taker_fee) / (1 + maker_fee) - 1 )
                + p_sl.loc[t]  * ( (1 - l) * (1 - taker_fee) / (1 + maker_fee) - 1 ))
        ev_list.append(ev)

        # 2) exit logic
        next_i   = full_index.get_loc(t) + 1
        mid_next = mid_f.iloc[next_i] if next_i < len(full_index) else mid_f.loc[t]
        alive    = deque()
        for pos in positions:
            reason   = None
            exit_mid = mid_f.loc[t]

            # if t >= pos["expire_time"]:
            #     reason = 'timeout'
            # elif exit_mid >= pos["tp_price"]:
            if exit_mid >= pos["tp_price"]:
                reason = 'tp'
            elif exit_mid <= pos["sl_price"]:
                reason = 'sl'

            if reason is not None:
                # determine base price for fee calculation
                base_exit = pos['tp_price'] if reason == 'tp' else exit_mid
                fee_rate  = maker_fee if reason == 'tp' else taker_fee
                exit_price = base_exit * (1 - fee_rate)
                pnl        = pos['size'] * (exit_price - pos['entry_price'])
                cash      += pos['size'] * pos['entry_price'] + pnl

                trade_log.append({
                    'entry_time':  pos['entry_time'],
                    'exit_time':   t,
                    'entry_mid':   pos['entry_mid'],
                    'exit_mid':    exit_mid,
                    'entry_price': pos['entry_price'],
                    'exit_price':  exit_price,
                    'base_exit':   base_exit,
                    'fee_entry':   pos['size'] * (pos['entry_price'] - pos['entry_mid']),
                    'fee_exit':    pos['size'] * (base_exit - exit_price),
                    'size':        pos['size'],
                    'pnl':         pnl,
                    'reason':      reason
                })
            else:
                alive.append(pos)
        positions = alive

        # force-close final positions
        if t == idx_bt[-1] and positions:
            for pos in positions:
                base_exit   = mid_f.loc[t]
                exit_price  = base_exit * (1 - taker_fee)
                pnl         = pos['size'] * (exit_price - pos['entry_price'])
                cash       += pos['size'] * pos['entry_price'] + pnl
                trade_log.append({
                    'entry_time':  pos['entry_time'],
                    'exit_time':   t,
                    'entry_mid':   pos['entry_mid'],
                    'exit_mid':    base_exit,
                    'entry_price': pos['entry_price'],
                    'exit_price':  exit_price,
                    'base_exit':   base_exit,
                    'fee_entry':   pos['size'] * (pos['entry_price'] - pos['entry_mid']),
                    'fee_exit':    pos['size'] * (base_exit - exit_price),
                    'size':        pos['size'],
                    'pnl':         pnl,
                    'reason':      'timeout'
                })
            positions.clear()

        # 3) entry logic
        if cash > 0.0 and (t - last_entry_time) >= entry_cd:
            # entry_mid   = mid_next
            entry_mid   = mid_f.loc[t]
            entry_price = entry_mid * (1 + maker_fee)
            pt, pn, ps  = p_tp.loc[t], p_none.loc[t], p_sl.loc[t]

            tp_exit_price   = tp_price.loc[t] * (1 - maker_fee)
            sl_exit_price   = sl_price.loc[t] * (1 - taker_fee)
            none_exit_price = entry_mid       * (1 - taker_fee)
            # none_exit_price = (tp_price.loc[t] + sl_price.loc[t]) / 2 * (1 - taker_fee)

            r_tp   = (tp_exit_price   - entry_price) / entry_price
            r_sl   = (sl_exit_price   - entry_price) / entry_price
            r_none = (none_exit_price - entry_price) / entry_price

            mean_r = pt*r_tp + pn*r_none + ps*r_sl
            var_r  = pt*(r_tp-mean_r)**2 + pn*(r_none-mean_r)**2 + ps*(r_sl-mean_r)**2
            f_star = mean_r/var_r if (var_r > 0) else 0.0
            f_star = max(min(f_star,1.0), 0.0)

            if f_star > threshold:
                # print(f"mean_r={mean_r}, pt={pt}, pn={pn}, ps={ps}, r_tp={r_tp}, r_none={r_none}, r_sl={r_sl}")
                # print(f"ev={ev}, pt={p_tp.loc[t]}, pn={p_none.loc[t]}, ps={p_sl.loc[t]}, r_tp={( (1 + g) * (1 - maker_fee) / (1 + maker_fee) - 1 )}, r_none={( (2+g-l)/2 * (1 - taker_fee) / (1 + maker_fee) - 1 )}, r_sl={( (1 - l) * (1 - taker_fee) / (1 + maker_fee) - 1 )}")
                dollar = f_star * cash
                size   = dollar / entry_price
                cash  -= size * entry_price
                positions.append({
                    'entry_time':  t,
                    'entry_mid':   entry_mid,
                    'entry_price': entry_price,
                    'size':        size,
                    'tp_price':    tp_price.loc[t],
                    'sl_price':    sl_price.loc[t],
                    'expire_time': t + pd.Timedelta(lag*10, unit='s')
                })
                last_entry_time = t
                trade_log.append({
                    'entry_time':  t,
                    'exit_time':   pd.NaT,
                    'entry_mid':   entry_mid,
                    'exit_mid':    np.nan,
                    'entry_price': entry_price,
                    'exit_price':  np.nan,
                    'base_exit':   np.nan,
                    'fee_entry':   size * (entry_price - entry_mid),
                    'fee_exit':    np.nan,
                    'size':        size,
                    'pnl':         np.nan,
                    'reason':      'entry'
                })

        # 4) record equity
        eq = cash + sum(p['size'] * mid_f.loc[t] for p in positions)
        pos_flag.append((eq-cash)/eq)
        equity_list.append(eq)

    equity_results[label] = pd.Series(equity_list, index=idx_bt)

    df_trades = pd.DataFrame(trade_log)
    if len(trade_log) == 0:
        print(f"[{label}] No trades occurred in this backtest window.")
    print(f"\n=== Trade Log, lag={label} Total {len(df_trades)} trades ===")
    print(df_trades)
    # Summary statistics for each exit reason
    if not df_trades.empty and 'reason' in df_trades.columns:
        df_stat = df_trades[df_trades['reason']!='entry']
        for r in ['tp','sl','timeout']:
            mask_r = df_stat['reason']==r
            count = mask_r.sum()
            avg_pnl = df_stat.loc[mask_r, 'pnl'].mean()
            print(f"{r.upper()} Total Count: {count}, Average PnL: {avg_pnl:.8f}")
    else:
        print("No trades recorded or missing reason column; skipping statistics.")

    # 5) Consistency check for fees & PnL with mismatch reporting
    df_check = pd.DataFrame(trade_log)
    if not df_check.empty:
        # calculate theoretical fees and pnl
        df_check['theo_fee_entry'] = df_check['size'] * df_check['entry_mid'] * maker_fee
        df_check['theo_fee_exit']  = df_check['size'] * (df_check['base_exit'] - df_check['exit_price'])
        df_check['computed_pnl']   = df_check['size'] * (df_check['exit_price'] - df_check['entry_price'])
        # only validate rows with an exit
        mask_valid = df_check['reason'] != 'entry'
        mask_entry = mask_valid & ~np.isclose(df_check['fee_entry'], df_check['theo_fee_entry'], atol=1e-8)
        mask_exit  = mask_valid & ~np.isclose(df_check['fee_exit'],  df_check['theo_fee_exit'],   atol=1e-8)
        mask_pnl   = mask_valid & ~np.isclose(df_check['pnl'],      df_check['computed_pnl'],    atol=1e-12)
        if mask_entry.any() or mask_exit.any() or mask_pnl.any():
            print("Mismatch detected in the following exited trades:")
            print(df_check.loc[mask_entry | mask_exit | mask_pnl,
                                ['entry_time','exit_time','reason',
                                'fee_entry','theo_fee_entry',
                                'fee_exit','theo_fee_exit',
                                'pnl','computed_pnl']])
    else:
        print("No trades to check for fees & PnL.")

    # extract entry/exit points for plotting
    if not df_trades.empty and 'reason' in df_trades.columns:
        entries = [(r["entry_time"], r["entry_price"])  for _,r in df_trades[df_trades["reason"]=="entry"].iterrows()]
        exits   = [(r["exit_time"],  r["exit_price"])  for _,r in df_trades[df_trades["reason"]!="entry"].iterrows()]
    else:
        entries = []
        exits = []

    # prepare series for plotting
    ts    = idx_bt
    pos_s = pd.Series(pos_flag,    index=ts)
    eq_s  = pd.Series(equity_list, index=ts)
    ev_s  = pd.Series(ev_list,     index=ts)

    # 4-panel plot
    fig, axes = plt.subplots(4,1, figsize=(14,12), sharex=True)

    # 4.1 Midprice + signals
    axes[0].plot(ts, mid_f.reindex(ts), 'k-')
    if entries:
        te, pe = zip(*entries)
        axes[0].scatter(te, pe, marker='^', c='g')
    if exits:
        tx, px = zip(*exits)
        axes[0].scatter(tx, px, marker='v', c='r')
    axes[0].set_title(f"lag={label} ── Mid & Signals")
    axes[0].grid(True)

    # 4.2 Probabilities & EV
    ax1 = axes[1]
    if len(ts) > 0:
        ax1.plot(ts, p_tp.reindex(ts),    label='P(TP)')
        ax1.plot(ts, p_none.reindex(ts),  label='P(None)')
        ax1.plot(ts, p_sl.reindex(ts),    label='P(SL)')
        ax2 = ax1.twinx()
        ax2.plot(ts, ev_s, 'k', lw=1.2, label='EV')
        h1, l1 = ax1.get_legend_handles_labels()
        h2, l2 = ax2.get_legend_handles_labels()
        ax1.legend(h1+h2, l1+l2, loc='upper left')
    else:
        ax1.text(0.5, 0.5, "No Probability Data", ha="center", va="center")
    ax1.set_title("Probabilities & EV")
    ax1.grid(True)

    # 4.3 Position flag
    if len(pos_s) > 0:
        axes[2].step(ts, pos_s, where='post', color='gray')
    else:
        axes[2].text(0.5, 0.5, "No Position Data", ha="center", va="center")
    axes[2].set_title("Position")
    axes[2].grid(True)

    # 4.4 Equity curve
    if len(eq_s) > 0:
        axes[3].plot(ts, eq_s, color='orange')
    else:
        axes[3].text(0.5, 0.5, "No Equity Data", ha="center", va="center")
    axes[3].set_title("Equity Curve")
    axes[3].grid(True)

    plt.tight_layout()
    plt.show()
