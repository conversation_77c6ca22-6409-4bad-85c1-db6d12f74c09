##
##
## Preliminary Analysis - Signal Mining: OBI Space Analysis
##
##


#
# Import necessary libraries
#

import pandas as pd
import numpy as np 
import matplotlib.pyplot as plt
import matplotlib.ticker as ticker
import seaborn as sns

import os
import sys
import glob
from pathlib import Path
import pyarrow
import warnings
warnings.filterwarnings('ignore')

from statsmodels.graphics.tsaplots import plot_acf

#
# Read Sample Data
#

def read_data(directory):
    """
    Reads data from Kraken directory.
    """

    # Create a dictionary to hold the sample data
    obi_data = {}

    # Define the path to the sample data
    sample_data_path = os.path.join(directory)
    
    # Read all feather files in the directory
    sample_files = glob.glob(os.path.join(sample_data_path, '*.feather'))
    
    # Load the first file as a sample
    cum_ticker = 1
    for file in sample_files:
        print(f"Reading file ({cum_ticker}/{len(sample_files)}): {file[file.find('H3B Trading Data')+17:]}...")
        df = pd.read_feather(file)
        coin_name = file[file.find('H3B Trading Data')+17:file.find('_USD')]

        if ('orderbook' in file):
            if (coin_name in obi_data):
                obi_data[coin_name] = pd.concat([obi_data[coin_name], df], axis=0)
            else:
                obi_data[coin_name] = df
        else:
            pass

        cum_ticker += 1
    
    # Sort the data by Time and Price
    for coin in obi_data:
        obi_data[coin] = obi_data[coin].sort_values(by=['Time', 'Price']).reset_index(drop=True)
        obi_data[coin]['Size'] = obi_data[coin]['Size'].astype(float)
        obi_data[coin]['Price'] = obi_data[coin]['Price'].astype(float)

    return obi_data

def calculate_mid_price(obi):
    """
    Calculates the mid price from the best bid and ask prices.
    """

    best_bid = obi.groupby('Time').apply(lambda g: g.iloc[len(g)//2-1]).reset_index(drop=True)
    best_ask = obi.groupby('Time').apply(lambda g: g.iloc[len(g)//2]).reset_index(drop=True)
    
    mid_price = (best_bid['Price'] + best_ask['Price']) / 2
    mid_price_df = pd.DataFrame({
        'Time': best_bid['Time'],
        'Mid_Price': mid_price
    })
    
    return mid_price_df

def calculate_obi_snapshot(obi, segment_size, method):
    """
    Calculates OBI snapshot.
    """

    if method == 'individual':
        obi_metrics = obi
    elif method == 'sum':
        lower_obi = np.array(obi.iloc[:, :40//2].iloc[:,::-1].cumsum(axis = 1))
        upper_obi = np.array(obi.iloc[:, 40//2:].cumsum(axis = 1))
        obi_metrics = (lower_obi - upper_obi) / (lower_obi + upper_obi)
    elif method == 'reg_fit':
        coef_list = []
        for time in obi.index:
            g = obi.loc[time]
            x = np.arange(len(g))
            y = g.values

            x_ = x[:len(x)//2]
            y_ = y[:len(y)//2]
            _x = x[len(x)//2:]
            _y = y[len(y)//2:]

            coefs_ = np.polyfit(x_, y_, 5)
            _coefs = np.polyfit(_x, _y, 5)

            coefs = np.concatenate((coefs_, _coefs))
            coef_list.append(coefs)

        obi_metrics = np.vstack(coef_list) if coef_list else np.array([])

    elif method == 'reg_fit_sum':
        for time in obi['Time'].unique():
            g = obi[obi['Time'] == time]
            x = np.arange(len(g['Vol Adjusted Size']))
            y = g['Vol Adjusted Size'].values

            x_ = x[:len(x)//2]
            y_ = y[:len(g['Vol Adjusted Size'])//2][::-1].cumsum()[::-1]  # a=5 introduces skewness
            _x = x[len(x)//2:]
            _y = y[len(y)//2:].cumsum()

            coefs_ = np.polyfit(x_, y_, 5)
            _coefs = np.polyfit(_x, _y, 5)

            coefs = np.concatenate((coefs_, _coefs))

            try:
                coef_mat = np.vstack([coef_mat, coefs])
            except:
                coef_mat = coefs
        obi_metrics = coef_mat

    else:
        raise ValueError("Method does not exist.")
    
    return obi_metrics

def volume_sum_by_price_bins_with_mid(obi_data, mid_price_df, segment_size):
    """
    Bin 'Price' symmetrically around mid price using cutoffs from mid_price_df.
    Sum 'Size' per bin, fill missing bins with zero.
    Return DataFrame: Time index, bin columns.
    """

    def process_group(g):
        time = g['Time'].iloc[0]
        row = mid_price_df.loc[mid_price_df['Time'] == time]

        if row.empty:
            print(f"❌ No mid price row for Time: {time}")
            return pd.Series(0.0, index=range(segment_size))

        lower = row['Lower Cutoff'].iloc[0]
        upper = row['Upper Cutoff'].iloc[0]

        if pd.isnull(lower) or pd.isnull(upper) or lower >= upper:
            print(f"⚠️ Invalid cutoffs at Time: {time}, Lower: {lower}, Upper: {upper}")
            return pd.Series(0.0, index=range(segment_size))

        # Create bins between lower and upper cutoff (segment_size bins)
        bins = np.linspace(lower, upper, segment_size + 1)

        # Digitize prices into bins (0-based bin indices)
        bin_indices = np.digitize(g['Price'], bins, right=False) - 1
        bin_indices = np.clip(bin_indices, 0, segment_size - 1)

        # Sum sizes per bin
        sums = pd.Series(0.0, index=range(segment_size))
        sums = sums.add(pd.Series(g['Size'].values, index=bin_indices).groupby(level=0).sum(), fill_value=0)

        return sums

    # Apply to each time group
    result = obi_data.groupby('Time').apply(process_group)

    # Clean index and columns
    result.index.name = 'Time'
    result.columns = [f'Bin_{i}' for i in range(segment_size)]

    return result

def calculate_obi_metrices(obi, mid_price_df, segment_size, cutoff):
    """
    Calculates OBI metrices.
    """
    # Asserting Segement Size need to be even
    assert segment_size % 2 == 0, "Segment size must be even."

    # Decide Everyday's Cutoff
    mid_price_df['Upper Cutoff'] = cutoff * mid_price_df['Mid_Price'] + mid_price_df['Mid_Price']
    mid_price_df['Lower Cutoff'] = -cutoff * mid_price_df['Mid_Price'] + mid_price_df['Mid_Price']

    # Confine the OBI data to cutoff then segment it into segments of equal size, aggregate the size of each segment.
    obi = obi.groupby('Time').apply(lambda g: g[g['Price'].between(mid_price_df[mid_price_df['Time'] == g['Time'].iloc[0]]['Lower Cutoff'].iloc[0],
                                                                   mid_price_df[mid_price_df['Time'] == g['Time'].iloc[0]]['Upper Cutoff'].iloc[0])]).reset_index(drop = True)
    # obi = obi.groupby('Time').apply(lambda g: g[g['Price'].between(g['Price'].max if abs(g['Price'].max() - mid_price_df[mid_price_df['Time'] == g['Time'].iloc[0]]['Mid_Price'].iloc[0]) <= abs(g['Price'].min() - mid_price_df[mid_price_df['Time'] == g['Time'].iloc[0]]['Mid_Price'].iloc[0]) else mid_price_df[mid_price_df['Time'] == g['Time'].iloc[0]]['Mid_Price'].iloc[0] + abs(g['Price'].min() - mid_price_df[mid_price_df['Time'] == g['Time'].iloc[0]]['Mid_Price'].iloc[0]),
    #                                                                 g['Price'].min if abs(g['Price'].max() - mid_price_df[mid_price_df['Time'] == g['Time'].iloc[0]]['Mid_Price'].iloc[0]) > abs(g['Price'].min() - mid_price_df[mid_price_df['Time'] == g['Time'].iloc[0]]['Mid_Price'].iloc[0]) else mid_price_df[mid_price_df['Time'] == g['Time'].iloc[0]]['Mid_Price'].iloc[0] - abs(g['Price'].max() - mid_price_df[mid_price_df['Time'] == g['Time'].iloc[0]]['Mid_Price'].iloc[0]))]).reset_index(drop = True)
    obi = volume_sum_by_price_bins_with_mid(obi, mid_price_df, segment_size)

    # obi_metrics_individual_s = calculate_obi_snapshot(obi, segment_size, method = 'individual')
    obi_metrics_sum_s = calculate_obi_snapshot(obi, segment_size, method = 'sum')
    obi_metrics_reg_s = calculate_obi_snapshot(obi, segment_size, method = 'reg_fit')
    # obi_metrics_reg_sum_s = calculate_obi_snapshot(obi, segment_size, method = 'reg_fit_sum')  

    obi_metrics_sum = pd.DataFrame(obi_metrics_sum_s.tolist(), 
                                   columns = [f'Cum. Layer({i})' for i in range(len(obi_metrics_sum_s[0]))],
                                   index = obi.index)
    obi_metrics_reg = pd.DataFrame(obi_metrics_reg_s.tolist(), 
                                   columns = [f'Coef.({i})' for i in range(len(obi_metrics_reg_s[0]))],
                                   index = obi.index)
    
    # Normalize the OBI metrics
    
    return obi, obi_metrics_sum, obi_metrics_reg

def mid_price_fluctuation(obi_dict, windows_volest, windows_dist):
    """
    Analyzes price fluctuations in the Mid Price.
    """
    # Placeholder for analysis logic
    for coin, df in obi_dict.items():
        print(f"Analyzing mid-price fluctuations for {coin}")

        mid_price_df = calculate_mid_price(df)

        # Volatility Analysis
        for window in windows_volest:
            mid_price_df[f'Volatility({window*10}s Window)'] = mid_price_df['Mid_Price'].pct_change().rolling(window=window).std()
        
        # Plotting the Frequency Distribution of Mid Prices
        for window in windows_dist:
            plt.hist(mid_price_df['Mid_Price'].pct_change().rolling(window = window).sum()*100, 
                        edgecolor='black', bins=50, alpha=0.5, density=True, 
                        label=f"{window*10}s Return - 95% CI: {np.quantile(mid_price_df['Mid_Price'].pct_change().rolling(window = window).sum().dropna()*100, [0.025, 0.975])}")
        plt.title(f'Frequency Distribution of Mid Price Return for {coin}')
        plt.xlabel('Mid Price Return (%)')
        plt.ylabel('Density')
        plt.legend()
        plt.tight_layout()
        plt.show()

        # Plotting the mid-price fluctuation
        for window in windows_volest:
            plt.plot(mid_price_df['Time'], mid_price_df[f'Volatility({window*10}s Window)']*100*18**0.5, label=f'Volatility ({window*10}s Window)')
        plt.title(f'3 Min Mid Price Volatility for {coin}')
        plt.xlabel('Time')
        plt.ylabel('Volatility (%)')
        plt.gca().xaxis.set_major_locator(ticker.MultipleLocator(200))
        plt.xticks(rotation=90)
        plt.legend()
        plt.tight_layout()
        plt.show()
        break

def mid_price_correlation_with_obi(obi_dict, windows_ret, segment_size, cutoff):
    """
    Analyzes the correlation of mid-price with OBI data.
    """
    # Calculate mid-price and analyze correlation with OBI data
    for coin, df in obi_dict.items():

        # Calculate mid-price
        print(f"Calculating mid-price for {coin}...")
        mid_price_df = calculate_mid_price(df)

        # Genearate OBI metrices
        print(f"Generating OBI metrices for {coin}...")
        obi, obi_metrics_sum, obi_metrics_reg = calculate_obi_metrices(df, mid_price_df, segment_size, cutoff)

        # Genearate correlation analysis
        print(f"Genearate correlation analysis for {coin}...")
        # Set the time as index for mid_price_df
        mid_price_df = mid_price_df.set_index('Time', drop=True)

        # Calculate returns for mid-price
        for window in windows_ret:
            mid_price_df[f't+{window}min Return'] = mid_price_df['Mid_Price'].pct_change().rolling(window=window).sum().shift(-window)
        
        # Calculate the correlation with OBI data
        corr_sum = pd.concat([mid_price_df, obi_metrics_sum], axis=1).corr().loc[mid_price_df.columns[3:], obi_metrics_sum.columns].round(2)
        corr_reg = pd.concat([mid_price_df, obi_metrics_reg], axis=1).corr().loc[mid_price_df.columns[3:], obi_metrics_reg.columns].round(2)
        

    # Heatmap for just those
    fig, axes = plt.subplots(2, 1, figsize=(30, 16))
    sns.heatmap(corr_reg, 
                annot=True, 
                cmap='coolwarm', 
                vmin=-1, vmax=1, 
                annot_kws={"size": 13}, 
                ax=axes[0])
    axes[0].set_title('Reg. OBI vs Return Correlation Heatmap', fontsize=18)
    axes[0].tick_params(axis='x', labelrotation=45, labelsize=15)
    axes[0].tick_params(axis='y', labelsize=15)

    sns.heatmap(corr_sum, 
                annot=True, 
                cmap='coolwarm', 
                vmin=-1, vmax=1, 
                annot_kws={"size": 13}, 
                ax=axes[1])
    axes[1].set_title('Cumulative Reg. OBI vs Return Correlation Heatmap', fontsize=18)
    axes[1].tick_params(axis='x', labelrotation=45, labelsize=15)
    axes[1].tick_params(axis='y', labelsize=15)

    plt.tight_layout()
    plt.show()

    # Scatter plot for Non-Linear Regression OBI vs Mid Price Return
    for window in windows_ret:
        for columns in obi_metrics_sum.columns:
            plt.figure(figsize=(10, 6))
            sns.scatterplot(x=obi_metrics_sum[columns], y=mid_price_df[f't+{window}min Return'], markers='.')
            plt.title(f'Relationship between Mid Price Return and {columns} (Window: {window} min)', fontsize=16)
            plt.xlabel('Mid Price Return', fontsize=14)
            plt.ylabel(columns, fontsize=14)
            plt.grid(True)
            plt.tight_layout()
            plt.show()

        for columns in obi_metrics_reg.columns:
            plt.figure(figsize=(10, 6))
            sns.scatterplot(x=obi_metrics_reg[columns], y=mid_price_df[f't+{window}min Return'], markers='.')
            plt.title(f'Relationship between Mid Price Return and {columns} (Window: {window} min)', fontsize=16)
            plt.xlabel('Mid Price Return', fontsize=14)
            plt.ylabel(columns, fontsize=14)
            plt.grid(True)
            plt.tight_layout()
            plt.show()

    return mid_price_df, corr_reg, corr_reg_sum

if __name__ == "__main__":
    # Define the directory containing the sample data
    directory = r"C:\Users\<USER>\OneDrive\Desktop\H3B Trading Data"

    # Read the sample data
    obi_dict = read_data(directory)

    # Test calculation functionalities
    mid_price_df = calculate_mid_price(obi_dict['BTC'])
    mid_price_df, corr_reg, corr_reg_sum = mid_price_correlation_with_obi({'BTC': obi_dict['BTC']}, 
                                                                                        windows_ret = [1, 2, 3, 5, 6, 
                                                                                                        12, 18, 24, 30, 60, 
                                                                                                        90, 120, 180, 360, 540, 
                                                                                                        720, 900, 1080], 
                                                                                        segment_size = 40,
                                                                                        cutoff = 0.01)
    
    # Calculate model superiority
    print('Mean Absolute Correlation of Reg. Smoothing (D = 5) Approach:', corr_reg.abs().values.mean())
    print('Mean Absolute Correlation of Reg. Cum. Smoothing (D = 5) Approach:', corr_reg_sum.abs().values.mean())