import pandas as pd
import numpy as np
import glob
import matplotlib.pyplot as plt
import multiprocessing

from sklearn.model_selection import RandomizedSearchCV, TimeSeriesSplit
from sklearn.metrics import accuracy_score, recall_score, f1_score, confusion_matrix
from sklearn.metrics import precision_score
from xgboost import XGBClassifier
from collections import deque
from scipy.stats import uniform, randint
from sklearn.metrics import make_scorer
from tqdm import tqdm

# --------------------------------------------------------------------------------
# 0. 并行 & 参数
# --------------------------------------------------------------------------------
n_cpu        = multiprocessing.cpu_count()
n_jobs       = max(1, n_cpu - 3)
coin         = "BTC_USD"
selected_layers = list(range(1,21))
of_ma_windows   = [1,6,30,60,120,300]
maker_fee = 0.0006
taker_fee = 0.0016


# --------------------------------------------------------------------------------
# 1. 载入 Orderbook
# --------------------------------------------------------------------------------
print("=== 1. Loading orderbook files ===")
files = glob.glob(f"../kraken_data/{coin}_orderbook_*.feather")
dfs   = [pd.read_feather(f) for f in files]
df_ob = pd.concat(dfs, ignore_index=True)

# --------------------------------------------------------------------------------
# 2. 计算 OBI & Raw Volumes & Mid/Bid/Ask
# --------------------------------------------------------------------------------
print("=== 2. Computing OBI snapshots ===")
cutoff, seg, snap_sz = 0.01, 40, 1000
times, mids, bbs, bas = [], [], [], []
obi_list, vol_list   = [], []

for i in tqdm(range(len(df_ob)//snap_sz), desc="Snapshots"):
    snap = df_ob.iloc[i*snap_sz:(i+1)*snap_sz]
    srt  = snap.sort_values("Price").reset_index(drop=True)

    bids = srt.iloc[:500].nlargest(1,"Price")["Price"].iloc[0]
    asks = srt.iloc[500:].nsmallest(1,"Price")["Price"].iloc[0]
    mid  = 0.5*(bids+asks)

    lower, upper = mid*(1-cutoff), mid*(1+cutoff)
    rng = srt[srt["Price"].between(lower,upper)].copy()

    if rng.empty:
        obi_row = [np.nan]*len(selected_layers)
        vol_row = [np.nan]*2*len(selected_layers)
    else:
        bins = np.linspace(lower, upper, seg+1)
        rng["Bin"] = pd.cut(rng["Price"], bins=bins, labels=False, include_lowest=True)
        bybin = rng.groupby("Bin")["Size"].sum().reindex(range(seg), fill_value=0).values
        obi_row, vol_row = [], []
        half = seg//2
        for k in selected_layers:
            Vb = bybin[half-k:half].sum()
            Va = bybin[half:half+k].sum()
            obi_row.append((Vb-Va)/(Vb+Va) if (Vb+Va)!=0 else np.nan)
            vol_row += [Vb, Va]

    obi_list.append(obi_row)
    vol_list.append(vol_row)
    times.append(srt["Time"].iloc[0])
    mids.append(mid)
    bbs.append(bids)
    bas.append(asks)

times = pd.to_datetime(times)
col_obi = [f"OBI_{k}" for k in selected_layers]
df_obi  = pd.DataFrame(obi_list, index=times, columns=col_obi)
col_vol = sum([[f"BidVol_{k}",f"AskVol_{k}"] for k in selected_layers],[])
df_vol  = pd.DataFrame(vol_list, index=times, columns=col_vol)
mid_s   = pd.Series(mids, index=times, name="MidPrice")
bb_s    = pd.Series(bbs, index=times, name="BestBid")
ba_s    = pd.Series(bas, index=times, name="BestAsk")

# Round to 10s and dedupe
df_obi.index = df_obi.index.round("10S")
df_obi       = df_obi[~df_obi.index.duplicated(keep="last")]
df_vol.index = df_vol.index.round("10S")
df_vol       = df_vol[~df_vol.index.duplicated(keep="last")]
mid_s.index  = mid_s.index.round("10S"); mid_s  = mid_s[~mid_s.index.duplicated()]
bb_s.index   = bb_s.index.round("10S");  bb_s   = bb_s[~bb_s.index.duplicated()]
ba_s.index   = ba_s.index.round("10S");  ba_s   = ba_s[~ba_s.index.duplicated()]

# --------------------------------------------------------------------------------
# 3. Build uniform time grid
# --------------------------------------------------------------------------------
full_index = pd.date_range(start=df_obi.index.min(), end=df_obi.index.max(), freq="10S")

# --------------------------------------------------------------------------------
# 4. Aggregate trade ticks → order_flow & TradeCount
# --------------------------------------------------------------------------------
print("=== 4. Aggregating trades ===")
trade_files      = glob.glob(f"../kraken_data/{coin}_trades_*.feather")
order_flow_acc   = pd.Series(0.0, index=full_index, name="order_flow")
trade_count_10s  = pd.Series(0,   index=full_index, name="TradeCount_10s")

for f in tqdm(trade_files, desc="Trades"):
    df_t = pd.read_feather(f).rename(columns={"Price":"price","Size":"amount","Time":"time","Side":"side"})
    df_t["time"] = pd.to_datetime(df_t["time"])
    df_t["signed"] = np.where(df_t["side"].str.lower()=="buy", df_t["amount"], -df_t["amount"])
    df_t = df_t.set_index("time")
    flow10 = df_t["signed"].groupby(pd.Grouper(freq="10S")).sum().reindex(full_index, fill_value=0)
    cnt10  = df_t["signed"].groupby(pd.Grouper(freq="10S")).count().reindex(full_index, fill_value=0)
    order_flow_acc  = order_flow_acc.add(flow10, fill_value=0)
    trade_count_10s = trade_count_10s.add(cnt10,  fill_value=0)

order_flow_acc.fillna(0, inplace=True)

# --------------------------------------------------------------------------------
# 5. Build OF features
# --------------------------------------------------------------------------------
print("=== 5. Building OF features ===")
of_z     = (order_flow_acc - order_flow_acc.mean())/order_flow_acc.std()
of_diff  = of_z.diff().fillna(0)
of_cum1m = order_flow_acc.rolling(6,min_periods=1).sum()
df_of    = pd.concat([of_z.rename("OF_z"), of_diff.rename("OF_diff"), of_cum1m.rename("OF_cum1m")], axis=1)
for w in of_ma_windows:
    df_of[f"OF_z_ma_{w}"]    = of_z.rolling(w,min_periods=1).mean()
    df_of[f"OF_diff_ma_{w}"] = of_diff.rolling(w,min_periods=1).mean()

# --------------------------------------------------------------------------------
# 6. Forward-fill OBI, VOL, MID, BID, ASK
# --------------------------------------------------------------------------------
def ffill(s): return s.reindex(full_index).ffill(limit=2)
df_obi_f = ffill(df_obi)
df_vol_f = ffill(df_vol)
mid_f    = ffill(mid_s)
bb_f     = ffill(bb_s)
ba_f     = ffill(ba_s)

# Derived features
spread  = (ba_f - bb_f).rename("Spread")
ma_s    = mid_f.rolling(6,min_periods=1).mean()
ma_l    = mid_f.rolling(30,min_periods=1).mean()
ma_diff = (ma_s - ma_l).rename("MA_diff")
vol1m   = mid_f.pct_change().rolling(6,min_periods=1).std().rename("Vol1m")

# Trade feature
trade1m = trade_count_10s.rolling(6,min_periods=1).sum().rename("TradeCnt1m")
df_trade= trade1m.to_frame()

# --------------------------------------------------------------------------------
# 7. Prepare returns dict (for reference only)
# --------------------------------------------------------------------------------
# lags_dict = {"1min":6,"5min":30,"10min":60, "30min":180, "60min":360, "120min":720}
lags_dict = {"30min":180, "1hr":360, "2hr":720, "4hr":1440, "8hr":2880}
# lags_dict = {"8hr":2880}

# --------------------------------------------------------------------------------
# 8. Train classifiers
# --------------------------------------------------------------------------------
print("=== 8. Training classifiers ===")

model_dict = {
    # "RandomForest": RandomForestClassifier(n_jobs=n_jobs, random_state=42),
    # "Logistic":    LogisticRegression(max_iter=1000,n_jobs=n_jobs,random_state=42),
    "XGBoost":     XGBClassifier(objective="multi:softprob", num_class=3,
                                 eval_metric="mlogloss",
                                 n_jobs=n_jobs, random_state=42)
}
window_list     = [1, 5, 10, 30, 60, 180, 360]
# window_list     = [1, 5, 10, 30]
# window_list     = [1]
tscv            = TimeSeriesSplit(n_splits=3)
tp_mult, sl_mult= 1.5, 1.0

accuracy_matrix = pd.DataFrame(index=window_list, columns=lags_dict.keys(), dtype=float)
recall_matrix   = pd.DataFrame(index=window_list, columns=lags_dict.keys(), dtype=float)
f1_matrix       = pd.DataFrame(index=window_list, columns=lags_dict.keys(), dtype=float)
confusion_mats  = {}
preds           = {}

avg_vol_tp_tp_df = pd.DataFrame(index=window_list, columns=lags_dict.keys(), dtype=float)
avg_vol_tp_sl_df = pd.DataFrame(index=window_list, columns=lags_dict.keys(), dtype=float)

for W in tqdm(window_list, desc="MA windows"):
    obi_ma   = df_obi_f.rolling(W,min_periods=1).mean()
    vol_ma   = df_vol_f.rolling(W,min_periods=1).mean()
    der_ma   = pd.concat([spread, ma_diff, vol1m], axis=1).rolling(W,min_periods=1).mean()
    of_ma    = df_of.rolling(W,min_periods=1).mean()
    sum_ma   = order_flow_acc.rolling(W,min_periods=1).sum().reindex(full_index).ffill().rename(f"OF_cum_{W}")
    trade_ma = df_trade.rolling(W,min_periods=1).mean()

    for label,lag in tqdm(lags_dict.items(), desc=f"Horizons (W={W})", leave=False):
        # 生成三分类 label
        vol_h = mid_f.pct_change().rolling(window=lag, min_periods=1).std().fillna(0) * np.sqrt(lag)
        tp_price = mid_f*(1+tp_mult*vol_h)
        sl_price = mid_f*(1-sl_mult*vol_h)
        labs, times_l = [], []
        prs, tpv, slv = mid_f.values, tp_price.values, sl_price.values
        for i in range(len(prs)-lag):
            fut = prs[i+1:i+1+lag]
            t_hit = np.where(fut>=tpv[i])[0]
            s_hit = np.where(fut<=slv[i])[0]
            if  t_hit.size and (not s_hit.size or t_hit[0]<s_hit[0]):
                labs.append(1)
            elif s_hit.size and (not t_hit.size or s_hit[0]<t_hit[0]):
                labs.append(-1)
            else:
                labs.append(0)
            times_l.append(full_index[i])
        label_series = pd.Series(labs, index=times_l, name="label")
        # 映射到 0,1,2
        label_series = label_series.map({-1:0, 0:1, 1:2})

        # 特征对齐
        feats = pd.concat([obi_ma, vol_ma, der_ma, of_ma, sum_ma, trade_ma], axis=1)
        feats = feats.loc[label_series.index].dropna()
        y_lab = label_series.loc[feats.index]

        if y_lab.nunique()<2:
            continue

        # train/test split
        days      = feats.index.normalize().unique()
        test_days = np.sort(days)[-7:]
        test_start = test_days.min()
        test_end   = test_days.max()
        trn_idx   = ~feats.index.normalize().isin(test_days)
        tst_idx   =  feats.index.normalize().isin(test_days)
        X_tr, X_te = feats.loc[trn_idx], feats.loc[tst_idx]
        y_tr, y_te = y_lab.loc[trn_idx], y_lab.loc[tst_idx]
        if len(X_tr)<20 or len(X_te)<20: 
            continue

        # hyperparam search & train
        key, est = next(iter(model_dict.items()))
        param_dist, n_iter = {
            "XGBoost": ({"n_estimators":[100,200,500],"max_depth":[3,6,10],
                        "learning_rate":[0.01,0.05,0.1],"subsample":[0.5,1.0],
                        "colsample_bytree":[0.5,1.0], "reg_lambda":[1, 5, 10]}, 5)
        }[key]
        # param_dist, n_iter = {
        #     "XGBoost": ({"n_estimators":[500,1000, 2000],"max_depth":[10, 15, 20],
        #                 "learning_rate":[0.01,0.05,0.1],"subsample":[0.5,1.0],
        #                 "colsample_bytree":[0.5,1.0]}, 5)
        # }[key]

        def ev_from_proba(y_true, y_proba, vol_h_series):
            preds      = np.argmax(y_proba, axis=1)
            tp_mask    = preds == 2
            if not tp_mask.any(): return -np.inf

            long_prec   = np.mean(y_true[tp_mask] == 2)
            tp_sl_ratio = np.mean(y_true[tp_mask] == 0)
            p_none =      np.mean(y_true[tp_mask] == 1)

            mask_tp_tp = tp_mask & (y_true == 2)
            mask_tp_sl = tp_mask & (y_true == 0)

            v_tp = np.mean(vol_h_series[mask_tp_tp]) if mask_tp_tp.any() else 0.0
            v_sl = np.mean(vol_h_series[mask_tp_sl]) if mask_tp_sl.any() else 0.0

            return ( long_prec*(v_tp*tp_mult - 2*maker_fee)
                - p_none*(taker_fee + maker_fee)
                - tp_sl_ratio*(v_sl + taker_fee + maker_fee) )

        def ev_score(estimator, X_val, y_val):
            proba = estimator.predict_proba(X_val)   # shape = (n_samples, 3)
            vol_h_sub = vol_h.reindex(X_val.index).to_numpy()
            return ev_from_proba(y_val.to_numpy(), proba, vol_h_sub)
        search = RandomizedSearchCV(estimator=est,
                                    param_distributions=param_dist,
                                    n_iter=n_iter,
                                    cv=tscv,
                                    # scoring=ev_score,
                                    scoring="accuracy",
                                    return_train_score=True,
                                    n_jobs=n_jobs,
                                    random_state=42,
                                    verbose=1,
                                    refit=True)
        search.fit(X_tr, y_tr)
        best_mod = search.best_estimator_
        
        # 预测 & 评估
        y_pred = best_mod.predict(X_te)
        acc    = accuracy_score(y_te, y_pred)
        rec    = recall_score(y_te, y_pred, average="macro")
        f1s    = f1_score(y_te, y_pred, average="macro")
        cm     = confusion_matrix(y_te, y_pred, labels=[2,1,0])  # 2=TP,1=None,0=SL

        # New metrics:
        tp_pred_count  = np.sum(y_pred == 2)
        tp_true_count  = np.sum((y_pred == 2) & (y_te == 2))
        long_prec      = tp_true_count / tp_pred_count if tp_pred_count > 0 else np.nan
        tp_to_sl       = np.sum((y_pred == 2) & (y_te == 0))
        tp_sl_ratio    = tp_to_sl / tp_pred_count if tp_pred_count > 0 else np.nan

        vol_h_te = vol_h.reindex(X_te.index)
        mask_tp_tp     = (y_pred == 2) & (y_te == 2)
        avg_vol_tp_tp  = vol_h_te[mask_tp_tp].mean()
        mask_tp_sl     = (y_pred == 2) & (y_te == 0)
        avg_vol_tp_sl  = vol_h_te[mask_tp_sl].mean()
        
        # Store results
        avg_vol_tp_tp_df.loc[W, label] = avg_vol_tp_tp
        avg_vol_tp_sl_df.loc[W, label] = avg_vol_tp_sl
        accuracy_matrix.loc[W, label] = acc
        recall_matrix.loc[W, label]   = rec
        f1_matrix.loc[W, label]       = f1s
        confusion_mats[(W, label)]    = cm
        preds[(W, label)]             = (y_te, y_pred)

        # Console output
        print(f"[W={W}, lag={label}] Acc={acc:.3f}, Rec={rec:.3f}, F1={f1s:.3f}")
        print(f"  Long Precision (TP class): {long_prec:.3f}")
        print(f"  TP→SL ratio: {tp_sl_ratio:.3f}")
        print("Confusion matrix:\n", cm)

        best_idx = search.best_index_
        n_folds = tscv.get_n_splits()
        train_evs = [
            search.cv_results_[f"split{i}_train_score"][best_idx]
            for i in range(n_folds)
        ]
        val_evs = [
            search.cv_results_[f"split{i}_test_score"][best_idx]
            for i in range(n_folds)
        ]
        print("▶ 每个 fold 训练集 EV:", train_evs)
        print("▶ 每个 fold 验证集 EV:", val_evs)
        test_proba = best_mod.predict_proba(X_te)
        test_ev    = ev_from_proba(y_te.values, test_proba, vol_h_te)
        print(f"  ➤ Test 集 EV 分数: {test_ev:.6f}")

        # # 可视化 TP/SL 水平与波动
        # fig,(ax1,ax2)=plt.subplots(2,1,figsize=(12,6),sharex=True)
        # ax1.plot(full_index, mid_f,      label="Mid Price")
        # ax1.plot(full_index, tp_price,   "--", label="TP")
        # ax1.plot(full_index, sl_price,   "--", label="SL")
        # ax1.set_ylabel("Price"); ax1.legend(); ax1.grid(True)
        # ax2.plot(full_index, vol_h,      label="Volatility"); 
        # ax2.set_ylabel("Vol"); ax2.set_xlabel("Time"); ax2.legend(); ax2.grid(True)
        # plt.tight_layout(); plt.show()

# --------------------------------------------------------------------------------
# 10. 输出结果并可视化混淆矩阵 + long precision + TP→SL ratio
# --------------------------------------------------------------------------------
# 10.1 打印矩阵
print("Accuracy:\n", accuracy_matrix)
print("Recall:\n", recall_matrix)
print("F1 Score:\n", f1_matrix)
print("=== Avg vol_h when TP→TP ===")
print(avg_vol_tp_tp_df)
print("=== Avg vol_h when TP→SL ===")
print(avg_vol_tp_sl_df)

# 10.2 计算 long precision 与 TP→SL ratio 的矩阵（手动计算）
long_prec_df   = pd.DataFrame(index=window_list, columns=lags_dict.keys(), dtype=float)
tp_sl_ratio_df = pd.DataFrame(index=window_list, columns=lags_dict.keys(), dtype=float)
ev_df = pd.DataFrame(index=window_list, columns=lags_dict.keys(), dtype=float)

for (W, lag), (y_te, y_pred) in preds.items():
    # 手动计算 Long Precision（class 2）
    tp_pred_count = np.sum(y_pred == 2)                            # 预测为 TP 的总数
    tp_true_count = np.sum((y_pred == 2) & (y_te == 2))            # 真正 TP 且预测为 TP 的数
    long_prec     = tp_true_count / tp_pred_count if tp_pred_count > 0 else np.nan
    long_prec_df.loc[W, lag] = long_prec
    p_none        = np.sum(y_pred == 1) / len(y_pred)
    # 手动计算 TP→SL ratio
    tp_to_sl      = np.sum((y_pred == 2) & (y_te == 0))            # 预测 TP 但实际 SL
    tp_sl_ratio   = tp_to_sl / tp_pred_count if tp_pred_count > 0 else np.nan
    tp_sl_ratio_df.loc[W, lag] = tp_sl_ratio

    # 平均波动率
    v_tp = avg_vol_tp_tp_df.loc[W, lag]
    v_sl = avg_vol_tp_sl_df.loc[W, lag]

    EV = (
        long_prec*(v_tp*tp_mult - 2*maker_fee)
        - p_none*(taker_fee + maker_fee)
        - tp_sl_ratio*(v_sl + taker_fee + maker_fee)
    )
    ev_df.loc[W, lag] = EV

# 10.3 绘制多子图 Confusion Matrix，并在每个子图下方写指标
labels = ["TP","None","SL"]
n_rows = len(window_list)
n_cols = len(lags_dict)

fig, axes = plt.subplots(
    n_rows, n_cols,
    figsize=(n_cols*3.5, n_rows*3.5),
    constrained_layout=True
)

def get_ax(i, j):
    if n_rows == 1 and n_cols == 1:
        return axes
    if n_rows == 1:
        return axes[j]
    if n_cols == 1:
        return axes[i]
    return axes[i, j]

for i, W in enumerate(window_list):
    for j, lag in enumerate(lags_dict):
        ax = get_ax(i, j)
        cm = confusion_mats.get((W, lag))
        ax.clear()

        if cm is None:
            ax.text(0.5, 0.5, "No Data", ha="center", va="center", fontsize=12)
        else:
            im = ax.imshow(cm, cmap="Blues", vmin=0)
            cbar = fig.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
            cbar.ax.tick_params(labelsize=8)

            # 混淆矩阵数值
            for x in range(3):
                for y in range(3):
                    val = cm[x, y]
                    color = "white" if val > cm.max()/2 else "black"
                    ax.text(y, x, int(val), ha="center", va="center",
                            color=color, fontsize=8)

            # 指标：LongPrec, TP→SL, AvgVol TP→TP, AvgVol TP→SL
            lp    = long_prec_df.loc[W, lag]
            ratio = tp_sl_ratio_df.loc[W, lag]
            v_tp  = avg_vol_tp_tp_df.loc[W, lag]
            v_sl  = avg_vol_tp_sl_df.loc[W, lag]
            ev_pct= ev_df.loc[W, lag]*100
            ax.text(0.5, -0.3,
                    f"LongPrec={lp:.2f}\nTP→SL={ratio:.2f}"
                    f"\nAvgVol(TP→TP)={v_tp*100:.2f}%"
                    f"\nAvgVol(TP→SL)={v_sl*100:.2f}%"
                    f"EV={ev_pct:.2f}%",
                    transform=ax.transAxes,
                    ha="center", va="top",
                    fontsize=7,
                    bbox=dict(boxstyle="round,pad=0.2", fc="white", ec="gray"))

        ax.set_xticks(np.arange(3))
        ax.set_xticklabels(labels, fontsize=8)
        ax.set_yticks(np.arange(3))
        ax.set_yticklabels(labels, fontsize=8)
        ax.tick_params(length=0)
        ax.set_xlabel("Pred", fontsize=8)
        ax.set_ylabel("True", fontsize=8)
        ax.set_title(f"W={W}, {lag}", fontsize=9)

plt.show()

try:
    feat_names = best_mod.feature_names_in_.tolist()
except AttributeError:
    feat_names = best_mod.get_booster().feature_names
# 11. Backtest (with correct fee scaling, PnL logging, and mismatch reporting)
threshold     = 0.0
initial_cash  = 1.0
equity_results = {}

for W in window_list:
    for label, lag in lags_dict.items():
        entry_cd    = pd.Timedelta(seconds=lag * 10 / 16)
        trade_log = []

        # rebuild features exactly as in training
        obi_ma   = df_obi_f.rolling(W, min_periods=1).mean()
        vol_ma   = df_vol_f.rolling(W, min_periods=1).mean()
        der_ma   = pd.concat([spread, ma_diff, vol1m], axis=1).rolling(W, min_periods=1).mean()
        of_ma    = df_of.rolling(W, min_periods=1).mean()
        sum_ma   = order_flow_acc.rolling(W, min_periods=1).sum().reindex(full_index).ffill().rename(f"OF_cum_{W}")
        trade_ma = df_trade.rolling(W, min_periods=1).mean()
        feats    = pd.concat([obi_ma, vol_ma, der_ma, of_ma, sum_ma, trade_ma], axis=1)
        X_full   = feats.reindex(columns=feat_names).dropna()
        idx_full = X_full.index

        # restrict to test days
        mask_test = idx_full.normalize().isin(test_days)
        idx_bt    = idx_full[mask_test]

        # prepare probability series
        proba    = best_mod.predict_proba(X_full)
        proba_df = pd.DataFrame(proba, index=idx_full, columns=best_mod.classes_)
        p_sl     = proba_df.get(0, pd.Series(0.0, index=idx_full))
        p_none   = proba_df.get(1, pd.Series(0.0, index=idx_full))
        p_tp     = proba_df.get(2, pd.Series(0.0, index=idx_full))

        # compute forward vol and TP/SL levels
        vol_h    = mid_f.pct_change().rolling(lag, min_periods=1).std().fillna(0) * np.sqrt(lag)
        tp_price = mid_f * (1 + tp_mult * vol_h)
        sl_price = mid_f * (1 - sl_mult * vol_h)

        cash            = initial_cash
        positions       = deque()
        last_entry_time = idx_bt[0]
        ev_list, pos_flag, equity_list = [], [], []

        for t in idx_bt:
            # 1) compute EV
            g  = tp_mult * vol_h.loc[t]
            l  = sl_mult * vol_h.loc[t]
            ev = (p_tp.loc[t] * (g - 2*maker_fee)
                  - p_none.loc[t] * (taker_fee + maker_fee)
                  - p_sl.loc[t]  * (l + taker_fee + maker_fee))
            ev_list.append(ev)

            # 2) exit logic
            next_i   = full_index.get_loc(t) + 1
            mid_next = mid_f.iloc[next_i] if next_i < len(full_index) else mid_f.loc[t]
            alive    = deque()
            for pos in positions:
                reason   = None
                exit_mid = mid_f.loc[t]

                if t >= pos["expire_time"]:
                    reason = 'timeout'
                elif exit_mid >= pos["tp_price"]:
                    reason = 'tp'
                elif exit_mid <= pos["sl_price"]:
                    reason = 'sl'

                if reason is not None:
                    # determine base price for fee calculation
                    base_exit = pos['tp_price'] if reason == 'tp' else exit_mid
                    fee_rate  = maker_fee if reason == 'tp' else taker_fee
                    exit_price = base_exit * (1 - fee_rate)
                    pnl        = pos['size'] * (exit_price - pos['entry_price'])
                    cash      += pos['size'] * pos['entry_price'] + pnl

                    trade_log.append({
                        'entry_time':  pos['entry_time'],
                        'exit_time':   t,
                        'entry_mid':   pos['entry_mid'],
                        'exit_mid':    exit_mid,
                        'entry_price': pos['entry_price'],
                        'exit_price':  exit_price,
                        'base_exit':   base_exit,
                        'fee_entry':   pos['size'] * (pos['entry_price'] - pos['entry_mid']),
                        'fee_exit':    pos['size'] * (base_exit - exit_price),
                        'size':        pos['size'],
                        'pnl':         pnl,
                        'reason':      reason
                    })
                else:
                    alive.append(pos)
            positions = alive

            # force-close final positions
            if t == idx_bt[-1] and positions:
                for pos in positions:
                    base_exit   = mid_f.loc[t]
                    exit_price  = base_exit * (1 - taker_fee)
                    pnl         = pos['size'] * (exit_price - pos['entry_price'])
                    cash       += pos['size'] * pos['entry_price'] + pnl
                    trade_log.append({
                        'entry_time':  pos['entry_time'],
                        'exit_time':   t,
                        'entry_mid':   pos['entry_mid'],
                        'exit_mid':    base_exit,
                        'entry_price': pos['entry_price'],
                        'exit_price':  exit_price,
                        'base_exit':   base_exit,
                        'fee_entry':   pos['size'] * (pos['entry_price'] - pos['entry_mid']),
                        'fee_exit':    pos['size'] * (base_exit - exit_price),
                        'size':        pos['size'],
                        'pnl':         pnl,
                        'reason':      'timeout'
                    })
                positions.clear()

            # 3) entry logic
            if cash > 0.0 and (t - last_entry_time) >= entry_cd:
                entry_mid   = mid_next
                entry_price = entry_mid * (1 + maker_fee)
                pt, pn, ps  = p_tp.loc[t], p_none.loc[t], p_sl.loc[t]

                tp_exit_price   = tp_price.loc[t] * (1 - maker_fee)
                sl_exit_price   = sl_price.loc[t] * (1 - taker_fee)
                none_exit_price = mid_next           * (1 - taker_fee)

                r_tp   = (tp_exit_price   - entry_price) / entry_price
                r_sl   = (sl_exit_price   - entry_price) / entry_price
                r_none = (none_exit_price - entry_price) / entry_price

                mean_r = pt*r_tp + pn*r_none + ps*r_sl
                var_r  = pt*(r_tp-mean_r)**2 + pn*(r_none-mean_r)**2 + ps*(r_sl-mean_r)**2
                f_star = mean_r/var_r if (var_r > 0 and pt > 0.5) else 0.0
                f_star = max(min(f_star,0.1), 0.0)

                if f_star > threshold:
                    dollar = f_star * cash
                    size   = dollar / entry_price
                    cash  -= size * entry_price
                    positions.append({
                        'entry_time':  t,
                        'entry_mid':   entry_mid,
                        'entry_price': entry_price,
                        'size':        size,
                        'tp_price':    tp_price.loc[t],
                        'sl_price':    sl_price.loc[t],
                        'expire_time': t + pd.Timedelta(lag*10, unit='s')
                    })
                    last_entry_time = t
                    trade_log.append({
                        'entry_time':  t,
                        'exit_time':   pd.NaT,
                        'entry_mid':   entry_mid,
                        'exit_mid':    np.nan,
                        'entry_price': entry_price,
                        'exit_price':  np.nan,
                        'base_exit':   np.nan,
                        'fee_entry':   size * (entry_price - entry_mid),
                        'fee_exit':    np.nan,
                        'size':        size,
                        'pnl':         np.nan,
                        'reason':      'entry'
                    })

            # 4) record equity
            eq = cash + sum(p['size'] * mid_f.loc[t] for p in positions)
            pos_flag.append((eq-cash)/eq)
            equity_list.append(eq)

        equity_results[(W,label)] = pd.Series(equity_list, index=idx_bt)

        df_trades = pd.DataFrame(trade_log)
        print(f"\n=== 交易紀錄 W={W}, lag={label} 共 {len(df_trades)} 筆 ===")
        print(df_trades)
        # Summary statistics for each exit reason
        df_stat = pd.DataFrame(trade_log)
        df_stat = df_stat[df_stat['reason']!='entry']
        for r in ['tp','sl','timeout']:
            mask_r = df_stat['reason']==r
            count = mask_r.sum()
            avg_pnl = df_stat.loc[mask_r, 'pnl'].mean()
            print(f"{r.upper()} 總筆數: {count}, 平均 PnL: {avg_pnl:.8f}")

        # 5) Consistency check for fees & PnL with mismatch reporting
        df_check = pd.DataFrame(trade_log)
        # calculate theoretical fees and pnl
        df_check['theo_fee_entry'] = df_check['size'] * df_check['entry_mid'] * maker_fee
        df_check['theo_fee_exit']  = df_check['size'] * (df_check['base_exit'] - df_check['exit_price'])
        df_check['computed_pnl']   = df_check['size'] * (df_check['exit_price'] - df_check['entry_price'])
        # only validate rows with an exit
        mask_valid = df_check['reason'] != 'entry'
        mask_entry = mask_valid & ~np.isclose(df_check['fee_entry'], df_check['theo_fee_entry'], atol=1e-8)
        mask_exit  = mask_valid & ~np.isclose(df_check['fee_exit'],  df_check['theo_fee_exit'],   atol=1e-8)
        mask_pnl   = mask_valid & ~np.isclose(df_check['pnl'],      df_check['computed_pnl'],    atol=1e-12)
        if mask_entry.any() or mask_exit.any() or mask_pnl.any():
            print("Mismatch detected in the following exited trades:")
            print(df_check.loc[mask_entry | mask_exit | mask_pnl,
                               ['entry_time','exit_time','reason',
                                'fee_entry','theo_fee_entry',
                                'fee_exit','theo_fee_exit',
                                'pnl','computed_pnl']])



        # extract entry/exit points for plotting
        entries = [(r["entry_time"], r["entry_price"])  for _,r in df_trades[df_trades["reason"]=="entry"].iterrows()]
        exits   = [(r["exit_time"],  r["exit_price"])  for _,r in df_trades[df_trades["reason"]!="entry"].iterrows()]


        # prepare series for plotting
        ts    = idx_bt
        pos_s = pd.Series(pos_flag,    index=ts)
        eq_s  = pd.Series(equity_list, index=ts)
        ev_s  = pd.Series(ev_list,     index=ts)

        # 4-panel plot
        fig, axes = plt.subplots(4,1, figsize=(14,12), sharex=True)

        # 4.1 Midprice + signals
        axes[0].plot(ts, mid_f.reindex(ts), 'k-')
        if entries:
            te, pe = zip(*entries)
            axes[0].scatter(te, pe, marker='^', c='g')
        if exits:
            tx, px = zip(*exits)
            axes[0].scatter(tx, px, marker='v', c='r')
        axes[0].set_title(f"W={W}, lag={label} ── Mid & Signals")
        axes[0].grid(True)

        # 4.2 Probabilities & EV
        ax1 = axes[1]
        ax1.plot(ts, p_tp.reindex(ts),    label='P(TP)')
        ax1.plot(ts, p_none.reindex(ts),  label='P(None)')
        ax1.plot(ts, p_sl.reindex(ts),    label='P(SL)')
        ax2 = ax1.twinx()
        ax2.plot(ts, ev_s, 'k', lw=1.2, label='EV')
        h1, l1 = ax1.get_legend_handles_labels()
        h2, l2 = ax2.get_legend_handles_labels()
        ax1.legend(h1+h2, l1+l2, loc='upper left')
        ax1.set_title("Probabilities & EV")
        ax1.grid(True)

        # 4.3 Position flag
        axes[2].step(ts, pos_s, where='post', color='gray')
        # axes[2].set_yticks([0,10])
        axes[2].set_title("Position")
        axes[2].grid(True)

        # 4.4 Equity curve
        axes[3].plot(ts, eq_s, color='orange')
        axes[3].set_title("Equity Curve")
        axes[3].grid(True)

        plt.tight_layout()
        plt.show()
