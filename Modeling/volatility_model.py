import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from arch import arch_model
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from statsmodels.tsa.stattools import acf, pacf
from sklearn.metrics import mean_squared_error


import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
from sklearn.metrics import mean_squared_error
from skopt import gp_minimize
from skopt.space import Integer, Real
from skopt.utils import use_named_args
import matplotlib.pyplot as plt

def plot_acf_pacf_squared_returns(price, horizons = [1, 5, 10], lags=40):
    """
    Plot ACF and PACF of squared returns.

    Parameters:
        returns (array-like): Return series.
        lags (int): Number of lags to show.
    """
    for h in horizons:
        # Create forward h-period returns
        returns = (price.shift(-h) / price - 1).dropna()
        squared_returns = returns ** 2

        plt.figure(figsize=(14, 5))
        plt.subplot(1, 2, 1)
        plot_acf(squared_returns, lags=lags, zero=False, ax=plt.gca())
        plt.title('ACF of Squared Returns')

        plt.subplot(1, 2, 2)
        plot_pacf(squared_returns, lags=lags, zero=False, ax=plt.gca())
        plt.title('PACF of Squared Returns')

        plt.tight_layout()
        plt.show()

# ----- Data prep -----
def create_sequences(data, seq_len):
    X, y = [], []
    for i in range(len(data) - seq_len):
        X.append(data[i:i+seq_len])
        y.append(data[i+seq_len])
    return np.array(X), np.array(y)

# ----- LSTM model -----
class VolatilityLSTM(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers):
        super().__init__()
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        self.linear = nn.Linear(hidden_size, 1)
    def forward(self, x):
        out, _ = self.lstm(x)
        out = out[:, -1, :]  # last timestep
        return self.linear(out)

# ----- Prediction helper -----
def predict_lstm(model, returns, seq_len):
    model.eval()
    ret_scaled = returns.reshape(-1, 1)
    preds = []
    with torch.no_grad():
        for i in range(len(returns) - seq_len):
            seq = torch.tensor(ret_scaled[i:i+seq_len], dtype=torch.float32).unsqueeze(0)
            pred = model(seq).item()
            preds.append(pred)
    return np.array(preds)

# ----- Load data -----
ticker = "SPY"
start_date = "2000-01-01"
end_date = "2025-06-01"
data = yf.download(ticker, start=start_date, end=end_date)
price = data['Close'].dropna()

# ----- Prepare returns -----
horizon = 1  # daily returns
returns_full = (price.shift(-horizon) / price - 1).dropna()
forecast_horizon = 200
train_returns = returns_full[:-forecast_horizon].values.flatten()
test_returns = returns_full[-forecast_horizon:].values.flatten()

# ----- Objective function for Bayesian optimization -----
@use_named_args([
    Integer(10, 50, name='seq_len'),
    Integer(16, 128, name='hidden_size'),
    Integer(1, 3, name='num_layers'),
    Real(1e-4, 1e-2, prior='log-uniform', name='lr'),
])

def objective(seq_len, hidden_size, num_layers, lr):
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # Prepare training target (rolling 5-day variance on train returns)
    train_vol = pd.Series(train_returns).rolling(5).var().dropna().values

    # Align input data to match train_vol length
    input_data = train_returns[-len(train_vol):].reshape(-1, 1)

    # Now create sequences
    if len(input_data) < seq_len:
        return np.inf  # skip invalid config during bayesopt

    X_train, _ = create_sequences(input_data, seq_len)

    # Now trim train_vol to match X_train
    train_vol_adj = train_vol[seq_len:]  # since each sequence predicts variance at its end

    # Confirm shape match
    if len(X_train) != len(train_vol_adj):
        return np.inf  # skip bad config cleanly

    # Convert to tensors
    X_train_t = torch.tensor(X_train, dtype=torch.float32).to(device)
    y_train_t = torch.tensor(train_vol_adj, dtype=torch.float32).unsqueeze(1).to(device)

    train_ds = TensorDataset(X_train_t, y_train_t)
    train_loader = DataLoader(train_ds, batch_size=32, shuffle=True)
    
    model = VolatilityLSTM(input_size=1, hidden_size=int(hidden_size), num_layers=int(num_layers)).to(device)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    
    model.train()
    for epoch in range(30):
        for xb, yb in train_loader:
            xb, yb = xb.to(device), yb.to(device)
            optimizer.zero_grad()
            pred = model(xb)
            loss = criterion(pred, yb)
            loss.backward()
            optimizer.step()
    
    # Predict on test returns
    preds = predict_lstm(model, test_returns, seq_len)
    true_vol_test = pd.Series(test_returns).rolling(5).var().dropna().values
    
    # Align length
    min_len = min(len(preds), len(true_vol_test))
    preds = preds[:min_len]
    true_vol_test = true_vol_test[:min_len]
    
    mse = mean_squared_error(true_vol_test, preds)
    print(f"seq_len={seq_len}, hidden_size={hidden_size}, num_layers={num_layers}, lr={lr:.5f} -> MSE={mse:.6f}")
    return mse

# ----- Run Bayesian Optimization -----
from skopt import gp_minimize

search_space = [
    Integer(10, 50, name='seq_len'),
    Integer(16, 128, name='hidden_size'),
    Integer(1, 3, name='num_layers'),
    Real(1e-4, 1e-2, prior='log-uniform', name='lr'),
]

result = gp_minimize(objective, search_space, n_calls=20, random_state=42)

print("\nBest MSE:", result.fun)
print("Best params:", result.x)

# ----- Plot best model predictions -----
best_params = result.x
seq_len, hidden_size, num_layers, lr = best_params
seq_len = int(seq_len)
hidden_size = int(hidden_size)
num_layers = int(num_layers)
epochs = 30

# Retrain best model
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = VolatilityLSTM(input_size=1, hidden_size=hidden_size, num_layers=num_layers).to(device)
train_vol = pd.Series(train_returns).rolling(5).var().dropna().values
input_data = train_returns[-len(train_vol):].reshape(-1, 1)
X_train, _ = create_sequences(input_data, seq_len)
y_train = train_vol[seq_len:]
X_train_t = torch.tensor(X_train, dtype=torch.float32).to(device)
y_train_t = torch.tensor(y_train, dtype=torch.float32).unsqueeze(1).to(device)
train_ds = TensorDataset(X_train_t, y_train_t)
train_loader = DataLoader(train_ds, batch_size=32, shuffle=True)
criterion = nn.MSELoss()
optimizer = torch.optim.Adam(model.parameters(), lr=lr)

model.train()
for epoch in range(30):
    for xb, yb in train_loader:
        optimizer.zero_grad()
        pred = model(xb)
        loss = criterion(pred, yb)
        loss.backward()
        optimizer.step()

# Predict and plot
preds = predict_lstm(model, test_returns, seq_len)
true_vol_test = pd.Series(test_returns).rolling(5).var().dropna().values
min_len = min(len(preds), len(true_vol_test))
preds = preds[:min_len]
true_vol_test = true_vol_test[:min_len]

plt.figure(figsize=(14, 6))
plt.plot(true_vol_test, label="True rolling var (test)")
plt.plot(preds, label="LSTM predicted var")
plt.title("Volatility Prediction with LSTM (Best Hyperparams)")
plt.xlabel("Test Time Index")
plt.ylabel("Variance")
plt.legend()
plt.grid(True)
plt.show()