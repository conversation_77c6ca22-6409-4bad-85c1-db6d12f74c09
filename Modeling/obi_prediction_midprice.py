import pandas as pd
import numpy as np
import glob
import matplotlib.pyplot as plt
import multiprocessing

from sklearn.linear_model import Ridge, <PERSON>so 
from sklearn.ensemble import RandomForestRegressor
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from sklearn.metrics import r2_score
from sklearn.model_selection import RandomizedSearchCV, PredefinedSplit
from sklearn.metrics import accuracy_score, precision_score
from sklearn.model_selection import TimeSeriesSplit
from sklearn.base import clone
from scipy.stats import uniform, randint
from tqdm import tqdm  # progress bar

# --------------------------------------------------------------------------------
# Determine how many CPU cores to use (leave one core free)
# --------------------------------------------------------------------------------
n_cpu = multiprocessing.cpu_count()
n_jobs_to_use = max(1, n_cpu - 3)
coin = "BTC_USD"

# --------------------------------------------------------------------------------
# 1. Load and concatenate all orderbook feather files
# --------------------------------------------------------------------------------
print("\n=== 1. Loading and concatenating orderbook files ===")
orderbook_files = glob.glob(f"../kraken_data/{coin}_orderbook_*.feather")
print("Found files:", orderbook_files)
dfs_ob = [pd.read_feather(f) for f in orderbook_files]
df_ob = pd.concat(dfs_ob, ignore_index=True)

# --------------------------------------------------------------------------------
# 2. Compute cumulative OBI (layers 1..20), best_bid, best_ask, mid_price for each snapshot
# --------------------------------------------------------------------------------
print("\n=== 2. Computing cumulative OBI & bid/ask for each snapshot ===")
cutoff = 0.01        # ±1% price boundary
segment_size = 40    # divide ±1% range into 40 bins
snapshot_size = 1000 # each snapshot has 1000 rows (500 bids + 500 asks)

n_snapshots = len(df_ob) // snapshot_size
snapshot_times = []
mid_prices = []
best_bids = []
best_asks = []
obi_cumulative = []
selected_layers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]

for idx in tqdm(range(n_snapshots), desc="Computing OBI snapshots"):
    start = idx * snapshot_size
    snap = df_ob.iloc[start : start + snapshot_size]
    snap_sorted = snap.sort_values("Price", ascending=True).reset_index(drop=True)

    # Identify best bid and best ask
    bids_all = snap_sorted.iloc[: snapshot_size // 2].sort_values("Price", ascending=False)
    asks_all = snap_sorted.iloc[snapshot_size // 2 :].sort_values("Price", ascending=True)
    best_bid_price = bids_all.iloc[0]["Price"]
    best_ask_price = asks_all.iloc[0]["Price"]
    mid_price = 0.5 * (best_bid_price + best_ask_price)

    lower = mid_price * (1 - cutoff)
    upper = mid_price * (1 + cutoff)

    mask_range = snap_sorted["Price"].between(lower, upper)
    snap_in_range = snap_sorted.loc[mask_range].copy()

    if snap_in_range.empty:
        # If no orders in ±1%, record NaNs
        obi_cumulative.append([np.nan] * len(selected_layers))
        mid_prices.append(mid_price)
        best_bids.append(best_bid_price)
        best_asks.append(best_ask_price)
        snapshot_times.append(snap_sorted.iloc[0]["Time"])
        continue

    # Bin into 40 equal-width bins
    bins = np.linspace(lower, upper, segment_size + 1)
    snap_in_range["Bin"] = pd.cut(
        snap_in_range["Price"], bins=bins, labels=False, include_lowest=True
    )

    vol_by_bin = snap_in_range.groupby("Bin")["Size"].sum()
    vol_full = vol_by_bin.reindex(range(segment_size), fill_value=0).values

    # Compute cumulative OBI for k = 1..20
    half = segment_size // 2
    obi_this_snapshot = []
    for k in selected_layers:
        V_bid_k = vol_full[half - k : half].sum()
        V_ask_k = vol_full[half : half + k].sum()
        if (V_bid_k + V_ask_k) != 0:
            obi_k = (V_bid_k - V_ask_k) / (V_bid_k + V_ask_k)
        else:
            obi_k = np.nan
        obi_this_snapshot.append(obi_k)

    obi_cumulative.append(obi_this_snapshot)
    mid_prices.append(mid_price)
    best_bids.append(best_bid_price)
    best_asks.append(best_ask_price)
    snapshot_times.append(snap_sorted.iloc[0]["Time"])

snapshot_times = pd.to_datetime(snapshot_times)
mid_price_series = pd.Series(mid_prices, index=snapshot_times, name="MidPrice")
best_bid_series = pd.Series(best_bids,  index=snapshot_times, name="BestBid")
best_ask_series = pd.Series(best_asks,  index=snapshot_times, name="BestAsk")

col_names = [f"OBI_{k}" for k in selected_layers]
df_obi = pd.DataFrame(np.array(obi_cumulative), index=snapshot_times, columns=col_names)

# Round to nearest 10 seconds and deduplicate
df_obi.index = df_obi.index.round("10S")
df_obi = df_obi[~df_obi.index.duplicated(keep="last")]

mid_price_series.index = mid_price_series.index.round("10S")
mid_price_series = mid_price_series[~mid_price_series.index.duplicated(keep="last")]

best_bid_series.index = best_bid_series.index.round("10S")
best_bid_series = best_bid_series[~best_bid_series.index.duplicated(keep="last")]

best_ask_series.index = best_ask_series.index.round("10S")
best_ask_series = best_ask_series[~best_ask_series.index.duplicated(keep="last")]

# --------------------------------------------------------------------------------
# 3. Build a uniform 10-second time index spanning all snapshots
# --------------------------------------------------------------------------------
print("\n=== 3. Building uniform 10-second time index ===")
full_index = pd.date_range(
    start=snapshot_times.min().floor("10S"),
    end=snapshot_times.max().ceil("10S"),
    freq="10S"
)

# --------------------------------------------------------------------------------
# 4. Load and aggregate trade ticks into order_flow using full_index
# --------------------------------------------------------------------------------
print("\n=== 4. Loading and aggregating trade tick files ===")
trade_files = glob.glob(f"../kraken_data/{coin}_trades_*.feather")

# Initialize order_flow_accum as a zero series indexed by full_index
order_flow_accum = pd.Series(0.0, index=full_index, name="order_flow")

for f in tqdm(trade_files, desc="Processing trade files"):
    df_t = pd.read_feather(f)
    df_t = df_t.rename(columns={"Price": "price", "Size": "amount", "Time": "time", "Side": "side"})
    df_t["time"] = pd.to_datetime(df_t["time"])

    # Compute signed_amount: buy → +amount, sell → -amount
    is_buy = df_t["side"].str.lower() == "buy"
    df_t["signed_amount"] = np.where(is_buy, df_t["amount"], -df_t["amount"])
    df_t = df_t[["time", "signed_amount"]]

    # Bin trades into 10-second intervals, then reindex to full_index
    df_t = df_t.set_index("time")
    flow_10s = df_t["signed_amount"].groupby(pd.Grouper(freq="10S")).sum()
    flow_10s = flow_10s.reindex(full_index, fill_value=0)

    # Accumulate
    order_flow_accum = order_flow_accum.add(flow_10s, fill_value=0)

order_flow_accum = order_flow_accum.fillna(0)

# --------------------------------------------------------------------------------
# 5. Standardize (z‐score) the aggregated order flow
# --------------------------------------------------------------------------------
print("\n=== 5. Standardizing order flow (z‐score) ===")
mean_flow = order_flow_accum.mean()
std_flow  = order_flow_accum.std()
order_flow_z = (order_flow_accum - mean_flow) / std_flow
order_flow_z.name = "order_flow_z"

# --------------------------------------------------------------------------------
# 6. Build extra order‐flow features (high‐frequency / cumulative + multi-window MA)
# --------------------------------------------------------------------------------
print("\n=== 6. Computing high‐frequency / cumulative order‐flow features ===")

# 6.1 Order‐Flow momentum: difference between current and previous 10‐second bin
order_flow_diff = order_flow_z.diff().fillna(0)
order_flow_diff.name = "OF_diff_10s"

# 6.2 Cumulative raw order_flow (not z‐scored) over past 1 minute (6 bins)
OF_1min = order_flow_accum.rolling(window=6, min_periods=1).sum()
OF_1min.name = "OF_cum_1min"

# 6.3 Define multiple MA windows (all in 10-second bins)
of_ma_windows = [1, 6, 30, 60, 120, 300]  # e.g., 1 minute, 5 minutes, 10 minutes, 20 minutes, 50 minutes

# 6.4 Build a single DataFrame with the original three features (z, diff, cum)
df_of_features = pd.concat([order_flow_z, order_flow_diff, OF_1min], axis=1)

# 6.5 Loop to calculate OF_z_ma and OF_diff_ma for each window
for w in of_ma_windows:
    df_of_features[f"OF_z_ma_{w}"]     = order_flow_z.rolling(window=w,     min_periods=1).mean()
    df_of_features[f"OF_diff_ma_{w}"]  = order_flow_diff.rolling(window=w,  min_periods=1).mean()

# --------------------------------------------------------------------------------
# 7. Reindex OBI, mid_price, best_bid, best_ask onto full_index and forward‐fill gaps
#    Order flow features already have no NaNs (we applied fillna)
# --------------------------------------------------------------------------------
print("\n=== 7. Reindexing and gap‐aware forward-fill ===")
df_obi_raw_full    = df_obi.reindex(full_index)
mid_price_raw_full = mid_price_series.reindex(full_index)
best_bid_raw_full  = best_bid_series.reindex(full_index)
best_ask_raw_full  = best_ask_series.reindex(full_index)

# Identify OBI timestamps in full_index
idx_locs_obi_full = full_index.get_indexer(df_obi.index.values)
idx_locs_obi      = idx_locs_obi_full[idx_locs_obi_full >= 0]

def limited_ffill(raw_series: pd.Series, idx_locs: np.ndarray, max_gap_steps: int) -> pd.Series:
    """
    Forward-fill within segments defined by idx_locs (original non-null positions).
    If idx_locs is empty, return the original series unchanged.
    """
    out = raw_series.copy()
    if idx_locs is None or len(idx_locs) == 0:
        return out

    sorted_locs = np.sort(idx_locs)
    gaps = np.diff(sorted_locs)
    gap_positions = np.where(gaps > max_gap_steps)[0]

    segments = []
    prev_loc = sorted_locs[0]
    for g in gap_positions:
        seg_start = prev_loc
        seg_end   = sorted_locs[g]
        segments.append((seg_start, seg_end))
        prev_loc = sorted_locs[g + 1]
    segments.append((prev_loc, sorted_locs[-1]))

    for (s, e) in segments:
        slice_range = out.iloc[s : e + 1]
        filled = slice_range.ffill()
        out.iloc[s : e + 1] = filled.values

    return out

max_gap_secs  = 20
max_gap_steps = max_gap_secs // 10  # = 2

df_obi_full     = limited_ffill(df_obi_raw_full,    idx_locs_obi, max_gap_steps)
mid_price_full  = limited_ffill(mid_price_raw_full, idx_locs_obi, max_gap_steps)
best_bid_full   = limited_ffill(best_bid_raw_full,  idx_locs_obi, max_gap_steps)
best_ask_full   = limited_ffill(best_ask_raw_full,  idx_locs_obi, max_gap_steps)
# order-flow features remain as-is (no forward-fill needed)
df_of_full      = df_of_features.copy()

# --------------------------------------------------------------------------------
# 8. Compute derived features: spread, MA diff, volatility
# --------------------------------------------------------------------------------
print("\n=== 8. Computing derived features (spread, MA diff, vol) ===")
# 8.1 Spread = best_ask - best_bid
spread = best_ask_full - best_bid_full
spread.name = "Spread"

# 8.2 Moving-average difference: short-term vs long-term on mid price
#     Short = 1-min MA (6 bins), Long = 5-min MA (30 bins)
MA_short = mid_price_full.rolling(window=6,  min_periods=1).mean()
MA_long  = mid_price_full.rolling(window=30, min_periods=1).mean()
MA_diff  = MA_short - MA_long
MA_diff.name = "MA10sec_minus_MA60sec"

# 8.3 Short-term volatility: rolling std over 1-minute window (6 bins)
vol_short = mid_price_full.rolling(window=6, min_periods=1).std()
vol_short.name = "Volatility1min"

df_derived = pd.concat([spread, MA_diff, vol_short], axis=1)

# --------------------------------------------------------------------------------
# 9. Compute future returns on mid_price_full (10-second grid)
# --------------------------------------------------------------------------------
print("\n=== 9. Computing returns on uniform grid ===")
df_returns_full = pd.DataFrame(index=full_index)
df_returns_full["10sec"]  = (mid_price_full.shift(-1)    / mid_price_full)  - 1
df_returns_full["30sec"]  = (mid_price_full.shift(-3)    / mid_price_full)  - 1
df_returns_full["1min"]   = (mid_price_full.shift(-6)    / mid_price_full)  - 1
df_returns_full["2min"]   = (mid_price_full.shift(-12)   / mid_price_full)  - 1
df_returns_full["3min"]   = (mid_price_full.shift(-18)   / mid_price_full)  - 1
df_returns_full["4min"]   = (mid_price_full.shift(-24)   / mid_price_full)  - 1
df_returns_full["5min"]   = (mid_price_full.shift(-30)   / mid_price_full)  - 1
df_returns_full["7.5min"] = (mid_price_full.shift(-45)   / mid_price_full)  - 1
df_returns_full["10min"]  = (mid_price_full.shift(-60)   / mid_price_full)  - 1
df_returns_full["30min"]  = (mid_price_full.shift(-180)  / mid_price_full)  - 1
df_returns_full["60min"]  = (mid_price_full.shift(-360)  / mid_price_full)  - 1
df_returns_full["180min"] = (mid_price_full.shift(-1080) / mid_price_full)  - 1
df_returns_full["300min"] = (mid_price_full.shift(-1800) / mid_price_full) - 1
# # --------------------------------------------------------------------------------
# # 10. Plot OBI_20, spread, and 1min return over time for quick inspection
# # --------------------------------------------------------------------------------
# print("\n=== 10. Plotting OBI_20, spread, and returns ===")
# obi_20       = df_obi_full["OBI_20"]
# return_label = "1min"
# ret_series   = df_returns_full[return_label]

# data_plot = pd.concat([obi_20, spread, ret_series], axis=1)
# data_plot.columns = ["OBI_20", "Spread", f"Return_{return_label}"]
# data_plot = data_plot.dropna()

# print("Number of rows in data_plot:", len(data_plot))
# print(data_plot.head(10))

# plt.figure(figsize=(12, 8))

# ax1 = plt.subplot(3, 1, 1)
# ax1.plot(data_plot.index, data_plot["OBI_20"], color="tab:blue", linewidth=0.7)
# ax1.set_ylabel("OBI_20")
# ax1.set_title("OBI_20 over Time")
# ax1.grid(True)

# ax2 = plt.subplot(3, 1, 2, sharex=ax1)
# ax2.plot(data_plot.index, data_plot["Spread"], color="tab:purple", linewidth=0.7)
# ax2.set_ylabel("Spread")
# ax2.set_title("Bid-Ask Spread over Time")
# ax2.grid(True)

# ax3 = plt.subplot(3, 1, 3, sharex=ax1)
# ax3.plot(data_plot.index, data_plot[f"Return_{return_label}"], color="tab:red", linewidth=0.7)
# ax3.set_ylabel(f"Return_{return_label}")
# ax3.set_title(f"Return ({return_label}) over Time")
# ax3.set_xlabel("Time")
# ax3.grid(True)

# plt.tight_layout()
# plt.show(block=True)
# input("Press Enter to continue to model training...")

# --------------------------------------------------------------------------------
# 11. Prepare data for each MA window and each horizon, then train models
# --------------------------------------------------------------------------------
print("\n=== 11. Training models for each MA window and each horizon ===")
# lags_dict = {
#     "1min": 6,
#     "3min": 18,
#     "5min": 30,
#     "10min": 60,
#     "30min": 180,
#     "60min": 360,
#     "180min": 1080,
#     "300min": 1800
# }
lags_dict = {
    "10sec": 1,
    "30sec": 3,
    "1min": 6,
    "2min": 12,
    "3min": 18,
    "4min": 24,
    "5min": 30,
    "7.5min": 45,
    "10min": 60,
}

# Toggle models by commenting/uncommenting below:
model_dict = {
    # "RandomForest": RandomForestRegressor(random_state=42, n_jobs=n_jobs_to_use),
    # "Ridge": Ridge(random_state=42),
    # "Lasso": Lasso(random_state=42),
    # "XGBoost": XGBRegressor(objective="reg:squarederror", random_state=42, n_jobs=n_jobs_to_use),
    "LightGBM":     LGBMRegressor(objective="regression", random_state=42, n_jobs=n_jobs_to_use)
}

# window_list = [1, 10, 30, 60, 120, 300]
window_list = [1, 2, 4, 8, 10, 15, 20]

best_r2_matrix     = pd.DataFrame(index=window_list, columns=lags_dict.keys(), dtype=float)
best_params_matrix = pd.DataFrame(index=window_list, columns=lags_dict.keys(), dtype=object)

# Will store (y_test, y_pred) for each (W, label) to plot later
preds = {}

# Parameter distributions for RandomForest (only if enabled)
lgb_param_dist = {
    "n_estimators":       randint(100, 1000),         # 森林大小
    "learning_rate":      uniform(0.01, 0.29),        # 0.01 ~ 0.3
    "num_leaves":         randint(20, 200),           # 葉節點數量
    "max_depth":          randint(3, 15),             # 樹深度
    "min_child_samples":  randint(5, 100),            # 每個葉節點最少樣本數
    "subsample":          uniform(0.5, 0.5),          # row sampling
    "colsample_bytree":   uniform(0.5, 0.5),          # feature sampling
    "reg_alpha":          uniform(0.0, 1.0),          # L1 正則化
    "reg_lambda":         uniform(0.0, 1.0),          # L2 正則化
    "min_split_gain":     uniform(0.0, 0.5),          # 分裂增益門檻
    "verbosity":          [-1]                        # 靜音
}
lgb_n_iter_search = 20

rf_param_dist = {
    "n_estimators": [100, 200, 500],
    "max_depth": [None, 10, 20],
    "min_samples_split": [2, 5, 10],
    "min_samples_leaf": [1, 5, 10],
    "max_features": ["sqrt", 0.3, None],
    "bootstrap": [True],
    "oob_score": [True],
    "criterion": ["squared_error"],
}
rf_n_iter_search = 20

# Parameter distributions for Ridge (only if enabled)
ridge_param_dist = {
    "alpha": [0.001, 0.01, 0.1, 1.0, 10.0],
    "fit_intercept": [True, False],
    "positive": [False, True]
}
ridge_n_iter_search = 20

# Parameter distributions for Lasso (only if enabled)
lasso_param_dist = {
    "alpha": [0.0001, 0.001, 0.01, 0.1, 1.0, 10.0],
    "fit_intercept": [True, False],
    "positive": [False, True]
}
lasso_n_iter_search = 20

# Parameter distributions for XGBoost (only if enabled)
# xgb_param_dist = {
#     "n_estimators": [100, 200, 500],
#     "max_depth": [3, 6, 10],
#     "learning_rate": [0.01, 0.05, 0.1],
#     "subsample": [0.5, 0.8, 1.0],
#     "colsample_bytree": [0.5, 0.8, 1.0],
#     "reg_lambda": [0.1, 1.0, 10.0]
# }
xgb_param_dist = {
    "n_estimators": [800, 1200, 1500, 2000],
    "max_depth": [20, 30, 50],
    "learning_rate": [0.2, 0.3],
    "subsample": [1.0],
    "colsample_bytree": [1.0],
    "reg_lambda": [0]
}
xgb_n_iter_search = 10

accuracy_matrix  = pd.DataFrame(index=window_list, columns=lags_dict.keys(), dtype=float)
precision_matrix = pd.DataFrame(index=window_list, columns=lags_dict.keys(), dtype=float)

for W in tqdm(window_list, desc="Processing MA windows"):
    # 11.1 rolling-window OBI
    df_obi_ma  = df_obi_full.rolling(window=W, min_periods=1).mean()

    # 11.2 rolling-window derived features (spread, MA diff, vol)
    derived_ma = df_derived.rolling(window=W, min_periods=1).mean()

    # 11.3 rolling-window order-flow z and diff
    of_ma_multi = df_of_full[[f"OF_z_ma_{win}" for win in of_ma_windows]
                             + [f"OF_diff_ma_{win}" for win in of_ma_windows]]

    # 11.4 W-sum of raw order flow
    of_sum = order_flow_accum.rolling(window=W, min_periods=1).sum() \
                              .reindex(full_index).ffill()
    of_sum.name = f"OF_cum_{W}"

    of_features_ma = df_of_features[
        [f"OF_z_ma_{w}"    for w in of_ma_windows]
      + [f"OF_diff_ma_{w}" for w in of_ma_windows]
    ]

    # now for each horizon
    for label, lag_val in tqdm(lags_dict.items(), desc=f"Horizons (W={W})", leave=False):
        # align and drop last lag
        X_obi     = df_obi_ma.iloc[:-lag_val]
        X_derived = derived_ma.iloc[:-lag_val]
        X_of      = of_ma_multi.iloc[:-lag_val]
        X_sum     = of_sum.iloc[:-lag_val].to_frame()
        y_series  = df_returns_full[label].iloc[:-lag_val]

        # concat and drop NaNs
        df_feats = pd.concat([X_obi, X_derived, X_of, X_sum, y_series], axis=1).dropna()
        df_X = df_feats.drop(columns=[label])
        y_curr   = y_series.loc[df_feats.index]   # align index

        # —— Correlation check —— 
        # Calculate absolute Pearson correlation
        corr_with_target = df_X.apply(lambda col: col.corr(y_curr)).abs().sort_values(ascending=False)
        print(f"[W={W}, {label}] Absolute correlation with returns:\n", corr_with_target.head(5))

        threshold = 0.02
        min_feats  = 3
        good_features = corr_with_target[corr_with_target > threshold].index.tolist()
        if len(good_features) < min_feats:
            good_features = corr_with_target.head(min_feats).index.tolist()
        df_X = df_X[good_features]
        feature_cols = good_features

        # # (Optional) Plot top 3 correlations
        # top3 = corr_with_target.head(3).index.tolist()
        # for feat in top3:
        #     plt.figure(figsize=(4,3))
        #     plt.scatter(df_feats[feat], y_curr, alpha=0.3, s=10)
        #     plt.title(f"W={W}, {label} | {feat} vs Returns (corr={corr_with_target[feat]:.3f})")
        #     plt.xlabel(feat); plt.ylabel("Returns"); plt.grid(True)
        #     plt.tight_layout(); plt.show()

        dates    = df_feats.index.normalize().unique()
        if len(dates) < 2:
            best_r2_matrix.loc[W, label]     = np.nan
            best_params_matrix.loc[W, label] = None
            continue

        # last-day holdout
        uniq_days = np.sort(df_feats.index.normalize().unique())
        test_days  = uniq_days[-2:]
        train_idx  = ~df_feats.index.normalize().isin(test_days)
        test_idx   =  df_feats.index.normalize().isin(test_days)
        train_hold = df_feats.loc[train_idx]
        test_hold  = df_feats.loc[test_idx]
        
        train_start, train_end = train_hold.index.min(), train_hold.index.max()
        test_start,  test_end  = test_hold.index.min(),  test_hold.index.max()
        print(
            f"W={W}, {label} → "
            f"train size={len(train_hold)} [{train_start} → {train_end}], "
            f"test size={len(test_hold)} [{test_start} → {test_end}]"
        )

        if len(train_hold)<20 or len(test_hold)<20:
            best_r2_matrix.loc[W, label]     = np.nan
            best_params_matrix.loc[W, label] = None
            continue

        # feature_cols = (
        #     col_names
        #     + list(df_derived.columns)
        #     + list(of_features_ma.columns)
        #     + [of_sum.name]
        # )
        X_train = train_hold[feature_cols].values
        y_train = train_hold[label].values
        X_test  = test_hold[feature_cols].values
        y_test  = test_hold[label].values

        # standardize returns
        y_mean = y_train.mean()
        y_std  = y_train.std()
        y_train_std = (y_train - y_mean) / y_std
        y_test_std = (y_test - y_mean) / y_std
        # predefined split
        X_all = np.vstack([X_train, X_test])
        y_all = np.concatenate([y_train, y_test])
        test_fold = np.concatenate([
            np.full(len(X_train), -1),
            np.zeros(len(X_test), dtype=int)
        ])
        ps = PredefinedSplit(test_fold)

        # pick model
        key = list(model_dict.keys())[0]
        est = model_dict[key]
        param_dist = {
            "RandomForest": (rf_param_dist,    rf_n_iter_search),
            "Ridge":        (ridge_param_dist, ridge_n_iter_search),
            "Lasso":        (lasso_param_dist, lasso_n_iter_search),
            "XGBoost":      (xgb_param_dist,   xgb_n_iter_search),
            "LightGBM":     (lgb_param_dist,   lgb_n_iter_search)
        }[key]

        tscv = TimeSeriesSplit(n_splits=5)
        search = RandomizedSearchCV(
            estimator=est,
            param_distributions=param_dist[0],
            n_iter=param_dist[1],
            cv=tscv,
            scoring="r2",
            n_jobs=n_jobs_to_use,
            random_state=42,
            verbose=0,
            refit=True
        )
        # search.fit(X_all, y_all)
        # search.fit(X_all, np.concatenate([y_train_std, y_test_std]))
        search.fit(X_train, y_train_std)

        best_p  = search.best_params_
        val_r2  = search.best_score_
        # best_mod = clone(est).set_params(**best_p)
        # best_mod.fit(X_train, y_train)
        best_mod = search.best_estimator_
        # best_mod.fit(X_train, y_train_std)

        # y_pred   = best_mod.predict(X_test)
        y_pred_std = best_mod.predict(X_test)
        y_pred = y_pred_std * y_std + y_mean
        test_r2  = r2_score(y_test, y_pred)
        acc      = accuracy_score((y_test>0).astype(int), (y_pred>0).astype(int))
        prec     = precision_score((y_test>0).astype(int), (y_pred>0).astype(int))

        best_r2_matrix.loc[W, label]     = test_r2
        best_params_matrix.loc[W, label] = str(best_p)
        accuracy_matrix.loc[W, label]    = acc
        precision_matrix.loc[W, label]   = prec

        print(
            f"[W={W}, {label}, {key}] "
            f"Val R²={val_r2:.4f} → Test R²={test_r2:.4f}, "
            f"Acc={acc:.3f}, Prec={prec:.3f}, Params={best_p}"
        )

        preds[(W, label)] = (y_test, y_pred)

        # feature-importance / coeff
        # best_mod = search.best_estimator_
        if hasattr(best_mod, "coef_"):
            # Ridge and Lasso go here
            coef_df = pd.DataFrame({
                "feature": feature_cols,
                "coef":    best_mod.coef_
            })
            coef_df = coef_df.reindex(
                coef_df["coef"].abs().sort_values(ascending=False).index
            ).head(5)
            print("Top-5 coefficients:\n", coef_df, "\n")
        else:
            # Tree models have feature_importances_
            imp_df = pd.DataFrame({
                "feature": feature_cols,
                "imp":     best_mod.feature_importances_
            }).sort_values("imp", ascending=False).head(5)
            print("Top-5 importances:\n", imp_df, "\n")

# --------------------------------------------------------------------------------
# 12. Plot predicted vs. actual returns for each (W, horizon)
# --------------------------------------------------------------------------------
print("\n=== 12. Plotting Predicted vs. Actual Returns ===")
horizons = list(lags_dict.keys())
n_rows = len(window_list)
n_cols = len(horizons)

fig, axes = plt.subplots(n_rows, n_cols, figsize=(4 * n_cols, 3 * n_rows), sharex=False, sharey=False)

def get_ax(axes, i, j):
    if n_rows == 1 and n_cols == 1:
        return axes
    elif n_rows == 1:
        return axes[j]
    elif n_cols == 1:
        return axes[i]
    else:
        return axes[i, j]

for i, W in enumerate(window_list):
    for j, label in enumerate(horizons):
        ax = get_ax(axes, i, j)
        key = (W, label)
        if key not in preds:
            ax.text(0.5, 0.5, "No Data", ha="center", va="center", fontsize=12, color="gray")
            ax.set_title(f"W={W}, {label}")
            ax.set_xlabel("Actual Return")
            ax.set_ylabel("Predicted Return")
            continue

        y_test, y_pred = preds[key]
        ax.scatter(y_test, y_pred, s=10, alpha=0.6)
        min_val = min(min(y_test), min(y_pred))
        max_val = max(max(y_test), max(y_pred))
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=1)

        r2   = best_r2_matrix.loc[W, label]
        acc  = accuracy_matrix.loc[W, label]
        prec = precision_matrix.loc[W, label]

        ax.text(
            0.05, 0.95,
            f"R²={r2:.3f}\nAcc={acc:.3f}\nPrec={prec:.3f}",
            transform=ax.transAxes,
            va="top", ha="left",
            bbox=dict(boxstyle="round,pad=0.3", fc="white", alpha=0.7),
            fontsize=9
        )
        
        ax.set_title(f"W={W}, {label}")
        ax.set_xlabel("Actual Return")
        ax.set_ylabel("Predicted Return")

plt.tight_layout()
plt.show()
