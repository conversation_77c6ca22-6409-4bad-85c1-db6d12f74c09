import json


def load_config(config_path):
    """
    Load configuration from JSON file

    Args:
        config_path (str): Path to configuration JSON file

    Returns:
        dict: Configuration dictionary containing:
            - start: start date
            - end: end date
            - ob_dir: orderbook data directory
            - trade_dir: trade data directory
            - coin: trading pair (e.g., "ETH_USD")
    """
    with open(config_path, "r") as f:
        config = json.load(f)
    return config
