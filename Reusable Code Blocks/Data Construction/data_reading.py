"""
Name: data_reading.py
Author: <PERSON>: read raw order book data and construct the orderbook volume dataframe
Input: Raw order book data (CSV), Raw transaction data (CSV), Configuration File (JSON)
Output: Raw OrderBook Dataframe (pd.Dataframe), Raw Transaction Dataframe (pd.Dataframe)

"""

#
# Import Packages & Set Environment
#

import pandas as pd
import numpy as np
import glob
import os
from tqdm import tqdm
from load_config import load_config

#
# Define Functions
#


def load_orderbook_data(config=load_config("config.json")):
    """
    Load and concatenate orderbook data files, then separate into bid and ask

    Args:
        config (dict): Configuration dictionary

    Returns:
        tuple: (bid_orderbook_df_s, ask_orderbook_df_s)
            - bid_orderbook_df_s: Bid orderbook data (cheapest 500, sorted by price descending)
            - ask_orderbook_df_s: Ask orderbook data (most expensive 500, sorted by price ascending)
    """
    coin = config['data_reading']["coin"]
    ob_dir = config['data_reading']["ob_dir"]

    print("=== Loading orderbook files ===")
    files = glob.glob(f"{ob_dir}/{coin}_orderbook_*.feather")

    if not files:
        raise FileNotFoundError(f"No orderbook files found in {ob_dir} for {coin}")

    dfs = []
    for f in tqdm(files, desc="Loading orderbook files"):
        df = pd.read_feather(f)
        dfs.append(df)

    df_ob_s = pd.concat(dfs, ignore_index=True)

    # Filter by date range if specified
    if "start" in config['data_reading'] and "end" in config['data_reading']:
        #df_ob_s["Time"] = pd.to_datetime(df_ob_s["Time"])
        mask = (df_ob_s["Time"] >= config['data_reading']["start"]) & (df_ob_s["Time"] <= config['data_reading']["end"])
        df_ob_s = df_ob_s[mask]

    print(f"Loaded {len(df_ob_s)} orderbook records")

    # Separate into bid and ask orderbooks
    print("=== Sorting orderbook price order ===")

    # Sort by price
    df_ob_s = df_ob_s.sort_values(by=['Time', 'Price']).reset_index(drop=True)

    return df_ob_s


def load_trade_data(config=load_config("config.json")):
    """
    Load and concatenate trade data files

    Args:
        config (dict): Configuration dictionary

    Returns:
        pd.DataFrame: Concatenated trade data with columns:
            - time: timestamp
            - price: trade price
            - amount: trade size
            - side: trade side (buy/sell)
    """
    coin = config['data_reading']["coin"]
    trade_dir = config['data_reading']["trade_dir"]

    print("=== Loading trade files ===")
    files = glob.glob(f"{trade_dir}/{coin}_trades_*.feather")

    if not files:
        raise FileNotFoundError(f"No trade files found in {trade_dir} for {coin}")

    dfs = []
    for f in tqdm(files, desc="Loading trade files"):
        df = pd.read_feather(f)
        # Standardize column names
        df = df.rename(
            columns={"Price": "price", "Size": "amount", "Time": "time", "Side": "side"}
        )
        dfs.append(df)

    df_trades_s = pd.concat(dfs, ignore_index=True)

    # Filter by date range if specified
    if "start" in config and "end" in config:
        #df_trades_s["time"] = pd.to_datetime(df_trades_s["time"])
        mask = (df_trades_s["time"] >= config['data_reading']["start"]) & (
            df_trades_s["time"] <= config['data_reading']["end"]
        )
        df_trades_s = df_trades_s[mask]

    print(f"Loaded {len(df_trades_s)} trade records")
    return df_trades_s


def main(config_path):
    """
    Main function to load orderbook and trade data

    Args:
        config_path (str): Path to configuration JSON file

    Returns:
        tuple: (bid_orderbook_df_s, ask_orderbook_df_s, trade_df_s)
            - bid_orderbook_df_s: Bid orderbook DataFrame
            - ask_orderbook_df_s: Ask orderbook DataFrame
            - trade_df_s: Raw trade DataFrame
    """
    # Load configuration
    config = load_config(config_path)

    # Load data
    orderbook_df_s = load_orderbook_data(config)
    trade_df_s = load_trade_data(config)

    return orderbook_df_s, trade_df_s


if __name__ == "__main__":
    # Example usage
    config_path = "config.json"
    #bid_orderbook_df_s, ask_orderbook_df_s, trade_df_s = main(config_path)
    df_ob_s = load_orderbook_data(config=load_config("config.json"))
    print(df_ob_s)
    # print(f"Bid orderbook data shape: {bid_orderbook_df_s.shape}")
    # print(f"Ask orderbook data shape: {ask_orderbook_df_s.shape}")
    # print(f"Trade data shape: {trade_df_s.shape}")
