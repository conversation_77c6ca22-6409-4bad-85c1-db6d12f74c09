"""
Name: ob_volume_construction
Author: <PERSON>: group raw order book data into bins and construct the orderbook volume dataframe
Input: Raw OrderBook Dataframe (pd.Dataframe), Midprice Series (pd.Series), Configuration File (JSON)
Output: Orderbook Volume Dataframe (CSV)
"""

#
# Import Packages & Set Environment
#

import pandas as pd
import numpy as np
from pathlib import Path
from load_config import load_config
from midprice_construction import calculate_mid_price
from data_reading import load_orderbook_data

#
# Define Fundctions
#

def volume_sum_by_price_bins_with_mid(ob_s, mid_price_s, upper_cutoff_s, lower_cutoff_s, segment_size):

    """
    Bin order book prices symmetrically around mid price, sum volumes per bin.

    Args:
        ob_s (pd.DataFrame): Order book snapshot dataframe with columns ['Time', 'Price', 'Size']
        mid_price_s (pd.DataFrame): Mid price dataframe with columns ['Time', 'Midprice']
        upper_cutoff_s (pd.Series): Series of upper cutoff prices indexed by mid_price_s['Time']
        lower_cutoff_s (pd.Series): Series of lower cutoff prices indexed by mid_price_s['Time']
        segment_size (int): Number of bins to segment the price range into (must be even)

    Returns:
        pd.DataFrame: DataFrame with Time index and bin columns ['Bin_0', 'Bin_1', ..., 'Bin_{segment_size-1}'], 
                      each containing summed order sizes within that bin for each time snapshot.
    """

    def process_group(g):
        time = g['Time'].iloc[0]
        row = mid_price_s.loc[mid_price_s['Time'] == time]

        if row.empty:
            print(f"❌ No mid price row for Time: {time}, filled with NA")
            return pd.Series(np.nan, index=range(segment_size))

        upper = upper_cutoff_s.loc[mid_price_s['Time'] == time].iloc[0]
        lower = lower_cutoff_s.loc[mid_price_s['Time'] == time].iloc[0]

        if pd.isnull(lower) or pd.isnull(upper) or lower >= upper:
            print(f"⚠️ Invalid cutoffs at Time: {time}, Lower: {lower}, Upper: {upper}, filled with NA")
            return pd.Series(np.nan, index=range(segment_size))

        # Create bins between lower and upper cutoff (segment_size bins)
        bins = np.linspace(lower, upper, segment_size + 1)

        # Digitize prices into bins (0-based bin indices)
        bin_indices = np.digitize(g['Price'], bins, right=False) - 1
        bin_indices = np.clip(bin_indices, 0, segment_size - 1)

        # Sum sizes per bin
        sums = pd.Series(0.0, index=range(segment_size))
        sums = sums.add(pd.Series(g['Size'].values, index=bin_indices).groupby(level=0).sum(), fill_value=0)

        return sums

    # Apply to each time group
    ob_volume_s = ob_s.groupby('Time').apply(process_group)

    # Clean index and columns
    ob_volume_s.index.name = 'Time'
    ob_volume_s.columns = [f'Bin_{i}' for i in range(segment_size)]

    return ob_volume_s

def ob_volume_construction(config=load_config("config.json")):

    """
    Constructs binned order book volume features based on mid price cutoffs, and saves to CSV.

    Args:
        config (dict): Configuration dictionary with the following keys:
            - ob_volumne_construction.segment_size (int): Number of price bins (must be even)
            - ob_volumne_construction.cutoff (float): Cutoff factor to determine price range around mid price
            - ob_volumne_construction.save_dir (str or Path): Path to save the resulting CSV file

    Returns:
        pd.DataFrame: DataFrame containing binned order book volume features 
                      with Time index and bin columns ['Bin_0', ..., 'Bin_{segment_size-1}'].
    """

    # Unpacking the config
    segment_size = config['ob_volumne_construction']['segment_size']
    cutoff = config['ob_volumne_construction']['cutoff']
    save_dir = config['ob_volumne_construction']['save_dir']

    # Create pre-required raw OB raw and mid price
    print('=== Loading pre-required files for orderbook volume construction ===')
    mid_price_df_s = calculate_mid_price(config)
    ob_df_s = load_orderbook_data(config)
    print('=== pre-required files loaded ===')

    # Checking configuration
    print('=== Checking configuration ===')
    if segment_size % 2 != 0:
        raise ValueError("Segment size must be even.")
    smallest_span = ob_df_s.groupby('Time').transform('count').min().iloc[0]
    if cutoff > smallest_span:
        raise ValueError(f"Cutoff value ({cutoff}) must be smaller than the smallest order book span ({smallest_span})")
    if not Path(save_dir).exists():
        raise FileNotFoundError(f"Save directory {save_dir} does not exist.")

    # Decide Everyday's Cutoff
    print('=== Binning orderbook volume ===')
    upper_cutoff_s = cutoff * mid_price_df_s['Midprice'] + mid_price_df_s['Midprice']
    lower_cutoff_s = -cutoff * mid_price_df_s['Midprice'] + mid_price_df_s['Midprice']

    # Confine the OBI data to cutoff then segment it into segments of equal size, aggregate the size of each segment.
    ob_df_s = ob_df_s.groupby('Time').apply(lambda g: g[g['Price'].between(lower_cutoff_s[mid_price_df_s['Time'] == g['Time'].iloc[0]].iloc[0],
                                                                        upper_cutoff_s[mid_price_df_s['Time'] == g['Time'].iloc[0]].iloc[0])]).reset_index(drop = True)
    print(ob_df_s)

    ob_volume_df_s = volume_sum_by_price_bins_with_mid(ob_df_s, mid_price_df_s, upper_cutoff_s, lower_cutoff_s, segment_size)

    print('=== Saving orderbook volume dataframe ===')
    ob_volume_df_s.to_csv(save_dir + '\\ob_volume_df_s.csv')

    return ob_volume_df_s

if __name__ == '__main__':
    ob_volume_df_s = ob_volume_construction()