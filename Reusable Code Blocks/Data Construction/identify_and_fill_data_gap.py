"""
Name: identify_and_fill_data_gap.py
Author: <PERSON>: <PERSON>gn pre-computed OBI and price data to uniform time intervals
Input: Pre-computed OBI Dataframe, Mid Price Series, Config File (JSON)
Output: Aligned OBI Dataframe, Aligned Mid Price Series, Uniform Time Grid

"""

#
# Import Packages & Set Environment
#

import pandas as pd
import numpy as np
from tqdm import tqdm
from load_config import load_config

#
# Define Functions
#


def create_uniform_time_grid(obi_df_s, mid_price_s, config):
    """
    Create a uniform time grid based on the data range and config

    Args:
        obi_df_s (pd.DataFrame): Pre-computed OBI data
        mid_price_s (pd.Series): Mid price series
        config (dict): Configuration dictionary

    Returns:
        pd.DatetimeIndex: Uniform time grid
    """
    print("=== Creating uniform time grid ===")

    # Get interval from config
    interval = config.get("interval", "10S")

    # Find the overall time range
    start_time = min(obi_df_s.index.min(), mid_price_s.index.min())
    end_time = max(obi_df_s.index.max(), mid_price_s.index.max())

    # Apply date filtering if specified in config
    if "start" in config:
        start_time = max(start_time, pd.to_datetime(config["start"]))
    if "end" in config:
        end_time = min(end_time, pd.to_datetime(config["end"]))

    # Create uniform time grid
    full_index = pd.date_range(start=start_time, end=end_time, freq=interval)

    print(f"Time range: {start_time} to {end_time}")
    print(f"Created {len(full_index)} time points with {interval} frequency")

    return full_index


def align_obi_data(obi_df_s, full_index, config):
    """
    Align OBI data to uniform time grid

    Args:
        obi_df_s (pd.DataFrame): Pre-computed OBI data
        full_index (pd.DatetimeIndex): Uniform time grid
        config (dict): Configuration dictionary

    Returns:
        pd.DataFrame: Aligned OBI data with uniform time index
    """
    print("=== Aligning OBI data ===")

    # Round timestamps to nearest interval
    interval = config.get("interval", "10S")
    obi_df_s.index = pd.to_datetime(obi_df_s.index)
    obi_df_s.index = obi_df_s.index.round(interval)

    # Remove duplicates by keeping the latest
    obi_df_s = obi_df_s[~obi_df_s.index.duplicated(keep="last")]

    # Reindex to full time grid
    obi_df_s = obi_df_s.reindex(full_index)

    # Apply filling method based on config
    filling_method = config.get("filling_method", "ffill")
    max_gap = config.get("max_gap", 2)

    if filling_method == "ffill":
        obi_df_s = obi_df_s.ffill(limit=max_gap)
    elif filling_method == "bfill":
        obi_df_s = obi_df_s.bfill(limit=max_gap)
    elif filling_method == "interpolate":
        obi_df_s = obi_df_s.interpolate(limit=max_gap)
    else:
        print(f"Warning: Unknown filling method '{filling_method}', using ffill")
        obi_df_s = obi_df_s.ffill(limit=max_gap)

    print(f"Original OBI records: {len(obi_df_s)}")
    print(f"After alignment: {len(obi_df_s)}")
    print(f"Filling method: {filling_method}, Max gap: {max_gap}")

    return obi_df_s


def align_mid_price_data(mid_price_s, full_index, config):
    """
    Align mid price data to uniform time grid

    Args:
        mid_price_s (pd.Series): Mid price series
        full_index (pd.DatetimeIndex): Uniform time grid
        config (dict): Configuration dictionary

    Returns:
        pd.Series: Aligned mid price series with uniform time index
    """
    print("=== Aligning mid price data ===")

    # Round timestamps to nearest interval
    interval = config.get("interval", "10S")
    mid_price_s.index = pd.to_datetime(mid_price_s.index)
    mid_price_s.index = mid_price_s.index.round(interval)

    # Remove duplicates by keeping the latest
    mid_price_s = mid_price_s[~mid_price_s.index.duplicated(keep="last")]

    # Reindex to full time grid
    mid_price_s = mid_price_s.reindex(full_index)

    # Apply filling method based on config
    filling_method = config.get("filling_method", "ffill")
    max_gap = config.get("max_gap", 2)

    if filling_method == "ffill":
        mid_price_s = mid_price_s.ffill(limit=max_gap)
    elif filling_method == "bfill":
        mid_price_s = mid_price_s.bfill(limit=max_gap)
    elif filling_method == "interpolate":
        mid_price_s = mid_price_s.interpolate(limit=max_gap)
    else:
        print(f"Warning: Unknown filling method '{filling_method}', using ffill")
        mid_price_s = mid_price_s.ffill(limit=max_gap)

    print(f"Original mid price records: {len(mid_price_s)}")
    print(f"After alignment: {len(mid_price_s)}")

    return mid_price_s


def identify_data_gaps(obi_df_s, mid_price_s, full_index):
    """
    Identify and report data gaps in the aligned datasets

    Args:
        obi_df_s (pd.DataFrame): Aligned OBI data
        mid_price_s (pd.Series): Aligned mid price series
        full_index (pd.DatetimeIndex): Uniform time grid

    Returns:
        dict: Gap statistics
    """
    print("=== Identifying data gaps ===")

    # Check for missing values in OBI data
    obi_missing = obi_df_s.isnull().sum()
    obi_gaps = obi_missing.sum()

    # Check for missing values in mid price data
    mid_price_gaps = mid_price_s.isnull().sum()

    # Calculate gap statistics
    total_periods = len(full_index)
    obi_gap_pct = (obi_gaps / total_periods) * 100
    mid_price_gap_pct = (mid_price_gaps / total_periods) * 100

    gap_stats = {
        "total_periods": total_periods,
        "obi_gaps": obi_gaps,
        "obi_gap_pct": obi_gap_pct,
        "mid_price_gaps": mid_price_gaps,
        "mid_price_gap_pct": mid_price_gap_pct,
    }

    print(f"Total time periods: {total_periods}")
    print(f"OBI gaps: {obi_gaps} ({obi_gap_pct:.2f}%)")
    print(f"Mid price gaps: {mid_price_gaps} ({mid_price_gap_pct:.2f}%)")

    return gap_stats


def main(obi_df_s, mid_price_s, config_path):
    """
    Main function to align pre-computed OBI and price data

    Args:
        obi_df_s (pd.DataFrame): Pre-computed OBI data
        mid_price_s (pd.Series): Mid price series
        config_path (str): Path to configuration JSON file

    Returns:
        tuple: (aligned_obi_df_s, aligned_mid_price_s, full_index, gap_stats)
            - aligned_obi_df_s: OBI data aligned to uniform time grid
            - aligned_mid_price_s: Mid price series aligned to uniform time grid
            - full_index: Uniform time grid
            - gap_stats: Statistics about data gaps
    """
    # Load configuration
    config = load_config(config_path)

    # Create uniform time grid
    full_index = create_uniform_time_grid(obi_df_s, mid_price_s, config)

    # Align data to uniform time grid
    aligned_obi_df_s = align_obi_data(obi_df_s, full_index, config)
    aligned_mid_price_s = align_mid_price_data(mid_price_s, full_index, config)

    # Identify data gaps
    gap_stats = identify_data_gaps(aligned_obi_df_s, aligned_mid_price_s, full_index)

    return aligned_obi_df_s, aligned_mid_price_s, full_index, gap_stats


if __name__ == "__main__":
    # Example usage
    # This would be called after data_reading.py has computed OBI and mid price

    # Example config.json structure:
    # {
    #     "interval": "10S",
    #     "filling_method": "ffill",
    #     "max_gap": 2,
    #     "start": "2024-01-01",
    #     "end": "2024-01-31"
    # }

    # Example data (these would come from data_reading.py processing)
    # obi_df_s = pd.DataFrame(...)  # Pre-computed OBI data
    # mid_price_s = pd.Series(...)  # Mid price series

    # Align data
    # aligned_obi_df_s, aligned_mid_price_s, time_grid, gaps = main(obi_df_s, mid_price_s, "config.json")

    print(
        "This module is designed to be imported and used with pre-computed OBI and price data"
    )
