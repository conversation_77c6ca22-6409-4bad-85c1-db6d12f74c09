import pandas as pd
from load_config import load_config
from data_reading import load_orderbook_data

def calculate_mid_price(config=load_config("config.json")):
    """
    Calculate mid price from orderbook data

    Args:
        config (JSON): configuration file

    Returns:
        pd.Series: Mid price series
    """
    print('=== Loading pre-required files for midprice calculation ===')
    ob_df_s = load_orderbook_data(config)

    best_bid_df_s = ob_df_s.groupby('Time').apply(lambda g: g.iloc[len(g)//2-1]).reset_index(drop=True)
    best_ask_df_s = ob_df_s.groupby('Time').apply(lambda g: g.iloc[len(g)//2]).reset_index(drop=True)
    
    print('=== Calculating midprice ===')
    mid_price = (best_bid_df_s['Price'] + best_ask_df_s['Price']) / 2
    mid_price_df_s = pd.DataFrame({
        'Time':   best_bid_df_s['Time'],
        'Midprice': mid_price
    })

    return mid_price_df_s
